{"instance_id": "rust__macros", "model_name_or_path": "20250510_021328--claude-3-5-sonnet-20241022_0", "model_patch": "diff --git a/src/lib.rs b/src/lib.rs\nindex a4c8c16..49a6ed0 100644\n--- a/src/lib.rs\n+++ b/src/lib.rs\n@@ -1,6 +1,36 @@\n+/// A macro to create a HashMap in an inline manner.\n+///\n+/// Example:\n+///\n+/// hashmap!('a' => 3, 'b' => 11, 'z' => 32);\n #[macro_export]\n macro_rules! hashmap {\n-    () => {\n-        todo!()\n-    };\n+    // Match zero or more key-value pairs with an optional trailing comma\n+    ( $( $key:expr => $value:expr ),* $(,)? ) => {{\n+         let mut hm = ::std::collections::HashMap::new();\n+         $( hm.insert($key, $value); )*\n+         hm\n+    }};\n+}\n+\n+// Make the macro available when the crate is imported\n+#[cfg(test)]\n+mod tests {\n+    use super::*;\n+\n+    #[test]\n+    fn test_hashmap_macro_empty() {\n+        let map: ::std::collections::HashMap<i32, i32> = hashmap!();\n+        assert!(map.is_empty());\n+    }\n+\n+    #[test]\n+    fn test_hashmap_macro() {\n+        let map = hashmap!('a' => 3, 'b' => 11, 'z' => 32,);\n+        let mut expected = ::std::collections::HashMap::new();\n+        expected.insert('a', 3);\n+        expected.insert('b', 11);\n+        expected.insert('z', 32);\n+        assert_eq!(map, expected);\n+    }\n }\ndiff --git a/Cargo.lock b/Cargo.lock\nnew file mode 100644\nindex 0000000..8821391\n--- /dev/null\n+++ b/Cargo.lock\n@@ -0,0 +1,7 @@\n+# This file is automatically @generated by Cargo.\n+# It is not intended for manual editing.\n+version = 4\n+\n+[[package]]\n+name = \"macros\"\n+version = \"0.1.0\"\ndiff --git a/target/.rustc_info.json b/target/.rustc_info.json\nnew file mode 100644\nindex 0000000..264502c\n--- /dev/null\n+++ b/target/.rustc_info.json\n@@ -0,0 +1 @@\n+{\"rustc_fingerprint\":15433756029299601208,\"outputs\":{\"13331785392996375709\":{\"success\":true,\"status\":\"\",\"code\":0,\"stdout\":\"___\\nlib___.rlib\\nlib___.so\\nlib___.so\\nlib___.a\\nlib___.so\\n/root/.rustup/toolchains/stable-x86_64-unknown-linux-gnu\\noff\\npacked\\nunpacked\\n___\\ndebug_assertions\\npanic=\\\"unwind\\\"\\nproc_macro\\ntarget_abi=\\\"\\\"\\ntarget_arch=\\\"x86_64\\\"\\ntarget_endian=\\\"little\\\"\\ntarget_env=\\\"gnu\\\"\\ntarget_family=\\\"unix\\\"\\ntarget_feature=\\\"fxsr\\\"\\ntarget_feature=\\\"sse\\\"\\ntarget_feature=\\\"sse2\\\"\\ntarget_has_atomic=\\\"16\\\"\\ntarget_has_atomic=\\\"32\\\"\\ntarget_has_atomic=\\\"64\\\"\\ntarget_has_atomic=\\\"8\\\"\\ntarget_has_atomic=\\\"ptr\\\"\\ntarget_os=\\\"linux\\\"\\ntarget_pointer_width=\\\"64\\\"\\ntarget_vendor=\\\"unknown\\\"\\nunix\\n\",\"stderr\":\"\"},\"17747080675513052775\":{\"success\":true,\"status\":\"\",\"code\":0,\"stdout\":\"rustc 1.85.1 (4eb161250 2025-03-15)\\nbinary: rustc\\ncommit-hash: 4eb161250e340c8f48f66e2b929ef4a5bed7c181\\ncommit-date: 2025-03-15\\nhost: x86_64-unknown-linux-gnu\\nrelease: 1.85.1\\nLLVM version: 19.1.7\\n\",\"stderr\":\"\"}},\"successes\":{}}\n\\ No newline at end of file\ndiff --git a/target/CACHEDIR.TAG b/target/CACHEDIR.TAG\nnew file mode 100644\nindex 0000000..20d7c31\n--- /dev/null\n+++ b/target/CACHEDIR.TAG\n@@ -0,0 +1,3 @@\n+Signature: 8a477f597d28d172789f06886806bc55\n+# This file is a cache directory tag created by cargo.\n+# For information about cache directory tags see https://bford.info/cachedir/\ndiff --git a/target/debug/.cargo-lock b/target/debug/.cargo-lock\nnew file mode 100644\nindex 0000000..e69de29\ndiff --git a/target/debug/.fingerprint/macros-3011f7f98e0e27f1/dep-test-lib-macros b/target/debug/.fingerprint/macros-3011f7f98e0e27f1/dep-test-lib-macros\nnew file mode 100644\nindex 0000000..024be49\nBinary files /dev/null and b/target/debug/.fingerprint/macros-3011f7f98e0e27f1/dep-test-lib-macros differ\ndiff --git a/target/debug/.fingerprint/macros-3011f7f98e0e27f1/invoked.timestamp b/target/debug/.fingerprint/macros-3011f7f98e0e27f1/invoked.timestamp\nnew file mode 100644\nindex 0000000..e00328d\n--- /dev/null\n+++ b/target/debug/.fingerprint/macros-3011f7f98e0e27f1/invoked.timestamp\n@@ -0,0 +1 @@\n+This file has an mtime of when this was started.\n\\ No newline at end of file\ndiff --git a/target/debug/.fingerprint/macros-3011f7f98e0e27f1/output-test-lib-macros b/target/debug/.fingerprint/macros-3011f7f98e0e27f1/output-test-lib-macros\nnew file mode 100644\nindex 0000000..f19bf31\n--- /dev/null\n+++ b/target/debug/.fingerprint/macros-3011f7f98e0e27f1/output-test-lib-macros\n@@ -0,0 +1,2 @@\n+{\"$message_type\":\"diagnostic\",\"message\":\"variable does not need to be mutable\",\"code\":{\"code\":\"unused_mut\",\"explanation\":null},\"level\":\"warning\",\"spans\":[{\"file_name\":\"src/lib.rs\",\"byte_start\":298,\"byte_end\":304,\"line_start\":10,\"line_end\":10,\"column_start\":14,\"column_end\":20,\"is_primary\":true,\"text\":[{\"text\":\"         let mut hm = ::std::collections::HashMap::new();\",\"highlight_start\":14,\"highlight_end\":20}],\"label\":null,\"suggested_replacement\":null,\"suggestion_applicability\":null,\"expansion\":{\"span\":{\"file_name\":\"src/lib.rs\",\"byte_start\":610,\"byte_end\":620,\"line_start\":23,\"line_end\":23,\"column_start\":58,\"column_end\":68,\"is_primary\":false,\"text\":[{\"text\":\"        let map: ::std::collections::HashMap<i32, i32> = hashmap!();\",\"highlight_start\":58,\"highlight_end\":68}],\"label\":null,\"suggested_replacement\":null,\"suggestion_applicability\":null,\"expansion\":null},\"macro_decl_name\":\"hashmap!\",\"def_site_span\":{\"file_name\":\"src/lib.rs\",\"byte_start\":136,\"byte_end\":156,\"line_start\":7,\"line_end\":7,\"column_start\":1,\"column_end\":21,\"is_primary\":false,\"text\":[{\"text\":\"macro_rules! hashmap {\",\"highlight_start\":1,\"highlight_end\":21}],\"label\":null,\"suggested_replacement\":null,\"suggestion_applicability\":null,\"expansion\":null}}}],\"children\":[{\"message\":\"`#[warn(unused_mut)]` on by default\",\"code\":null,\"level\":\"note\",\"spans\":[],\"children\":[],\"rendered\":null},{\"message\":\"remove this `mut`\",\"code\":null,\"level\":\"help\",\"spans\":[{\"file_name\":\"src/lib.rs\",\"byte_start\":298,\"byte_end\":302,\"line_start\":10,\"line_end\":10,\"column_start\":14,\"column_end\":18,\"is_primary\":true,\"text\":[{\"text\":\"         let mut hm = ::std::collections::HashMap::new();\",\"highlight_start\":14,\"highlight_end\":18}],\"label\":null,\"suggested_replacement\":\"\",\"suggestion_applicability\":\"MachineApplicable\",\"expansion\":{\"span\":{\"file_name\":\"src/lib.rs\",\"byte_start\":610,\"byte_end\":620,\"line_start\":23,\"line_end\":23,\"column_start\":58,\"column_end\":68,\"is_primary\":false,\"text\":[{\"text\":\"        let map: ::std::collections::HashMap<i32, i32> = hashmap!();\",\"highlight_start\":58,\"highlight_end\":68}],\"label\":null,\"suggested_replacement\":null,\"suggestion_applicability\":null,\"expansion\":null},\"macro_decl_name\":\"hashmap!\",\"def_site_span\":{\"file_name\":\"src/lib.rs\",\"byte_start\":136,\"byte_end\":156,\"line_start\":7,\"line_end\":7,\"column_start\":1,\"column_end\":21,\"is_primary\":false,\"text\":[{\"text\":\"macro_rules! hashmap {\",\"highlight_start\":1,\"highlight_end\":21}],\"label\":null,\"suggested_replacement\":null,\"suggestion_applicability\":null,\"expansion\":null}}}],\"children\":[],\"rendered\":null}],\"rendered\":\"\\u001b[0m\\u001b[1m\\u001b[33mwarning\\u001b[0m\\u001b[0m\\u001b[1m: variable does not need to be mutable\\u001b[0m\\n\\u001b[0m  \\u001b[0m\\u001b[0m\\u001b[1m\\u001b[38;5;12m--> \\u001b[0m\\u001b[0msrc/lib.rs:10:14\\u001b[0m\\n\\u001b[0m   \\u001b[0m\\u001b[0m\\u001b[1m\\u001b[38;5;12m|\\u001b[0m\\n\\u001b[0m\\u001b[1m\\u001b[38;5;12m10\\u001b[0m\\u001b[0m \\u001b[0m\\u001b[0m\\u001b[1m\\u001b[38;5;12m|\\u001b[0m\\u001b[0m \\u001b[0m\\u001b[0m         let mut hm = ::std::collections::HashMap::new();\\u001b[0m\\n\\u001b[0m   \\u001b[0m\\u001b[0m\\u001b[1m\\u001b[38;5;12m|\\u001b[0m\\u001b[0m              \\u001b[0m\\u001b[0m\\u001b[1m\\u001b[38;5;12m----\\u001b[0m\\u001b[0m\\u001b[1m\\u001b[33m^^\\u001b[0m\\n\\u001b[0m   \\u001b[0m\\u001b[0m\\u001b[1m\\u001b[38;5;12m|\\u001b[0m\\u001b[0m              \\u001b[0m\\u001b[0m\\u001b[1m\\u001b[38;5;12m|\\u001b[0m\\n\\u001b[0m   \\u001b[0m\\u001b[0m\\u001b[1m\\u001b[38;5;12m|\\u001b[0m\\u001b[0m              \\u001b[0m\\u001b[0m\\u001b[1m\\u001b[38;5;12mhelp: remove this `mut`\\u001b[0m\\n\\u001b[0m\\u001b[1m\\u001b[38;5;12m...\\u001b[0m\\n\\u001b[0m\\u001b[1m\\u001b[38;5;12m23\\u001b[0m\\u001b[0m \\u001b[0m\\u001b[0m\\u001b[1m\\u001b[38;5;12m|\\u001b[0m\\u001b[0m \\u001b[0m\\u001b[0m        let map: ::std::collections::HashMap<i32, i32> = hashmap!();\\u001b[0m\\n\\u001b[0m   \\u001b[0m\\u001b[0m\\u001b[1m\\u001b[38;5;12m|\\u001b[0m\\u001b[0m                                                          \\u001b[0m\\u001b[0m\\u001b[1m\\u001b[38;5;12m----------\\u001b[0m\\u001b[0m \\u001b[0m\\u001b[0m\\u001b[1m\\u001b[38;5;12min this macro invocation\\u001b[0m\\n\\u001b[0m   \\u001b[0m\\u001b[0m\\u001b[1m\\u001b[38;5;12m|\\u001b[0m\\n\\u001b[0m   \\u001b[0m\\u001b[0m\\u001b[1m\\u001b[38;5;12m= \\u001b[0m\\u001b[0m\\u001b[1mnote\\u001b[0m\\u001b[0m: `#[warn(unused_mut)]` on by default\\u001b[0m\\n\\u001b[0m   \\u001b[0m\\u001b[0m\\u001b[1m\\u001b[38;5;12m= \\u001b[0m\\u001b[0m\\u001b[1mnote\\u001b[0m\\u001b[0m: this warning originates in the macro `hashmap` (in Nightly builds, run with -Z macro-backtrace for more info)\\u001b[0m\\n\\n\"}\n+{\"$message_type\":\"diagnostic\",\"message\":\"1 warning emitted\",\"code\":null,\"level\":\"warning\",\"spans\":[],\"children\":[],\"rendered\":\"\\u001b[0m\\u001b[1m\\u001b[33mwarning\\u001b[0m\\u001b[0m\\u001b[1m: 1 warning emitted\\u001b[0m\\n\\n\"}\ndiff --git a/target/debug/.fingerprint/macros-3011f7f98e0e27f1/test-lib-macros b/target/debug/.fingerprint/macros-3011f7f98e0e27f1/test-lib-macros\nnew file mode 100644\nindex 0000000..62d822b\n--- /dev/null\n+++ b/target/debug/.fingerprint/macros-3011f7f98e0e27f1/test-lib-macros\n@@ -0,0 +1 @@\n+cadf1748bc7a2c16\n\\ No newline at end of file\ndiff --git a/target/debug/.fingerprint/macros-3011f7f98e0e27f1/test-lib-macros.json b/target/debug/.fingerprint/macros-3011f7f98e0e27f1/test-lib-macros.json\nnew file mode 100644\nindex 0000000..7ba601c\n--- /dev/null\n+++ b/target/debug/.fingerprint/macros-3011f7f98e0e27f1/test-lib-macros.json\n@@ -0,0 +1 @@\n+{\"rustc\":8750902384292223123,\"features\":\"[]\",\"declared_features\":\"[]\",\"target\":11051195766957558429,\"profile\":1722584277633009122,\"path\":10763286916239946207,\"deps\":[],\"local\":[{\"CheckDepInfo\":{\"dep_info\":\"debug/.fingerprint/macros-3011f7f98e0e27f1/dep-test-lib-macros\",\"checksum\":false}}],\"rustflags\":[],\"config\":2069994364910194474,\"compile_kind\":0}\n\\ No newline at end of file\ndiff --git a/target/debug/.fingerprint/macros-9309008d28f275da/dep-lib-macros b/target/debug/.fingerprint/macros-9309008d28f275da/dep-lib-macros\nnew file mode 100644\nindex 0000000..024be49\nBinary files /dev/null and b/target/debug/.fingerprint/macros-9309008d28f275da/dep-lib-macros differ\ndiff --git a/target/debug/.fingerprint/macros-9309008d28f275da/invoked.timestamp b/target/debug/.fingerprint/macros-9309008d28f275da/invoked.timestamp\nnew file mode 100644\nindex 0000000..e00328d\n--- /dev/null\n+++ b/target/debug/.fingerprint/macros-9309008d28f275da/invoked.timestamp\n@@ -0,0 +1 @@\n+This file has an mtime of when this was started.\n\\ No newline at end of file\ndiff --git a/target/debug/.fingerprint/macros-9309008d28f275da/lib-macros b/target/debug/.fingerprint/macros-9309008d28f275da/lib-macros\nnew file mode 100644\nindex 0000000..1f3e153\n--- /dev/null\n+++ b/target/debug/.fingerprint/macros-9309008d28f275da/lib-macros\n@@ -0,0 +1 @@\n+cc49ebef57d8b6d4\n\\ No newline at end of file\ndiff --git a/target/debug/.fingerprint/macros-9309008d28f275da/lib-macros.json b/target/debug/.fingerprint/macros-9309008d28f275da/lib-macros.json\nnew file mode 100644\nindex 0000000..e0a231c\n--- /dev/null\n+++ b/target/debug/.fingerprint/macros-9309008d28f275da/lib-macros.json\n@@ -0,0 +1 @@\n+{\"rustc\":8750902384292223123,\"features\":\"[]\",\"declared_features\":\"[]\",\"target\":11051195766957558429,\"profile\":8731458305071235362,\"path\":10763286916239946207,\"deps\":[],\"local\":[{\"CheckDepInfo\":{\"dep_info\":\"debug/.fingerprint/macros-9309008d28f275da/dep-lib-macros\",\"checksum\":false}}],\"rustflags\":[],\"config\":2069994364910194474,\"compile_kind\":0}\n\\ No newline at end of file\ndiff --git a/target/debug/deps/libmacros-9309008d28f275da.rlib b/target/debug/deps/libmacros-9309008d28f275da.rlib\nnew file mode 100644\nindex 0000000..9747a6f\nBinary files /dev/null and b/target/debug/deps/libmacros-9309008d28f275da.rlib differ\ndiff --git a/target/debug/deps/libmacros-9309008d28f275da.rmeta b/target/debug/deps/libmacros-9309008d28f275da.rmeta\nnew file mode 100644\nindex 0000000..8424ab7\nBinary files /dev/null and b/target/debug/deps/libmacros-9309008d28f275da.rmeta differ\ndiff --git a/target/debug/deps/macros-3011f7f98e0e27f1 b/target/debug/deps/macros-3011f7f98e0e27f1\nnew file mode 100755\nindex 0000000..83ebd1a\nBinary files /dev/null and b/target/debug/deps/macros-3011f7f98e0e27f1 differ\ndiff --git a/target/debug/deps/macros-3011f7f98e0e27f1.d b/target/debug/deps/macros-3011f7f98e0e27f1.d\nnew file mode 100644\nindex 0000000..8139894\n--- /dev/null\n+++ b/target/debug/deps/macros-3011f7f98e0e27f1.d\n@@ -0,0 +1,5 @@\n+/testbed/target/debug/deps/macros-3011f7f98e0e27f1: src/lib.rs\n+\n+/testbed/target/debug/deps/macros-3011f7f98e0e27f1.d: src/lib.rs\n+\n+src/lib.rs:\ndiff --git a/target/debug/deps/macros-9309008d28f275da.d b/target/debug/deps/macros-9309008d28f275da.d\nnew file mode 100644\nindex 0000000..ff94a71\n--- /dev/null\n+++ b/target/debug/deps/macros-9309008d28f275da.d\n@@ -0,0 +1,7 @@\n+/testbed/target/debug/deps/libmacros-9309008d28f275da.rmeta: src/lib.rs\n+\n+/testbed/target/debug/deps/libmacros-9309008d28f275da.rlib: src/lib.rs\n+\n+/testbed/target/debug/deps/macros-9309008d28f275da.d: src/lib.rs\n+\n+src/lib.rs:\ndiff --git a/target/debug/incremental/macros-1aoejw0naiy7r/s-h77dn4ifoy-1fk4nx0-6q55cqe085pj4i6fcyw40slvb/4wvkhl2xxvqmph7im1hneka9z.o b/target/debug/incremental/macros-1aoejw0naiy7r/s-h77dn4ifoy-1fk4nx0-6q55cqe085pj4i6fcyw40slvb/4wvkhl2xxvqmph7im1hneka9z.o\nnew file mode 100644\nindex 0000000..e331e3b\nBinary files /dev/null and b/target/debug/incremental/macros-1aoejw0naiy7r/s-h77dn4ifoy-1fk4nx0-6q55cqe085pj4i6fcyw40slvb/4wvkhl2xxvqmph7im1hneka9z.o differ\ndiff --git a/target/debug/incremental/macros-1aoejw0naiy7r/s-h77dn4ifoy-1fk4nx0-6q55cqe085pj4i6fcyw40slvb/dep-graph.bin b/target/debug/incremental/macros-1aoejw0naiy7r/s-h77dn4ifoy-1fk4nx0-6q55cqe085pj4i6fcyw40slvb/dep-graph.bin\nnew file mode 100644\nindex 0000000..4583907\nBinary files /dev/null and b/target/debug/incremental/macros-1aoejw0naiy7r/s-h77dn4ifoy-1fk4nx0-6q55cqe085pj4i6fcyw40slvb/dep-graph.bin differ\ndiff --git a/target/debug/incremental/macros-1aoejw0naiy7r/s-h77dn4ifoy-1fk4nx0-6q55cqe085pj4i6fcyw40slvb/query-cache.bin b/target/debug/incremental/macros-1aoejw0naiy7r/s-h77dn4ifoy-1fk4nx0-6q55cqe085pj4i6fcyw40slvb/query-cache.bin\nnew file mode 100644\nindex 0000000..a23842e\nBinary files /dev/null and b/target/debug/incremental/macros-1aoejw0naiy7r/s-h77dn4ifoy-1fk4nx0-6q55cqe085pj4i6fcyw40slvb/query-cache.bin differ\ndiff --git a/target/debug/incremental/macros-1aoejw0naiy7r/s-h77dn4ifoy-1fk4nx0-6q55cqe085pj4i6fcyw40slvb/work-products.bin b/target/debug/incremental/macros-1aoejw0naiy7r/s-h77dn4ifoy-1fk4nx0-6q55cqe085pj4i6fcyw40slvb/work-products.bin\nnew file mode 100644\nindex 0000000..9a69600\nBinary files /dev/null and b/target/debug/incremental/macros-1aoejw0naiy7r/s-h77dn4ifoy-1fk4nx0-6q55cqe085pj4i6fcyw40slvb/work-products.bin differ\ndiff --git a/target/debug/incremental/macros-1aoejw0naiy7r/s-h77dn4ifoy-1fk4nx0.lock b/target/debug/incremental/macros-1aoejw0naiy7r/s-h77dn4ifoy-1fk4nx0.lock\nnew file mode 100644\nindex 0000000..e69de29\ndiff --git a/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/18b46l4enf8yd214mjqtaq21h.o b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/18b46l4enf8yd214mjqtaq21h.o\nnew file mode 100644\nindex 0000000..b874133\nBinary files /dev/null and b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/18b46l4enf8yd214mjqtaq21h.o differ\ndiff --git a/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/1cecdpmyoxqpu6fyk57kvg3n0.o b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/1cecdpmyoxqpu6fyk57kvg3n0.o\nnew file mode 100644\nindex 0000000..baf3319\nBinary files /dev/null and b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/1cecdpmyoxqpu6fyk57kvg3n0.o differ\ndiff --git a/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/1muic7h5gnv8n66c5q41h5haq.o b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/1muic7h5gnv8n66c5q41h5haq.o\nnew file mode 100644\nindex 0000000..1c3d400\nBinary files /dev/null and b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/1muic7h5gnv8n66c5q41h5haq.o differ\ndiff --git a/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/1n1x1qqy5l60bo0lyc2815p3g.o b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/1n1x1qqy5l60bo0lyc2815p3g.o\nnew file mode 100644\nindex 0000000..344f112\nBinary files /dev/null and b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/1n1x1qqy5l60bo0lyc2815p3g.o differ\ndiff --git a/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/1pz6io20myhf7em1xwuxmglek.o b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/1pz6io20myhf7em1xwuxmglek.o\nnew file mode 100644\nindex 0000000..6edd0c9\nBinary files /dev/null and b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/1pz6io20myhf7em1xwuxmglek.o differ\ndiff --git a/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/1q80186o7kc68km5bp2uoj6t3.o b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/1q80186o7kc68km5bp2uoj6t3.o\nnew file mode 100644\nindex 0000000..19e518e\nBinary files /dev/null and b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/1q80186o7kc68km5bp2uoj6t3.o differ\ndiff --git a/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/1tr45xlnzw0fg7180jdyqr5jt.o b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/1tr45xlnzw0fg7180jdyqr5jt.o\nnew file mode 100644\nindex 0000000..9139ae4\nBinary files /dev/null and b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/1tr45xlnzw0fg7180jdyqr5jt.o differ\ndiff --git a/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/1wxhftj2gp055wwe9w3gf7oy1.o b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/1wxhftj2gp055wwe9w3gf7oy1.o\nnew file mode 100644\nindex 0000000..0cf5d86\nBinary files /dev/null and b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/1wxhftj2gp055wwe9w3gf7oy1.o differ\ndiff --git a/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/1x70nzwbeylc13zx01oivboaw.o b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/1x70nzwbeylc13zx01oivboaw.o\nnew file mode 100644\nindex 0000000..0c3f4f9\nBinary files /dev/null and b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/1x70nzwbeylc13zx01oivboaw.o differ\ndiff --git a/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/2xzm2b1qanc0luxk0m8fwzfay.o b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/2xzm2b1qanc0luxk0m8fwzfay.o\nnew file mode 100644\nindex 0000000..f113c5b\nBinary files /dev/null and b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/2xzm2b1qanc0luxk0m8fwzfay.o differ\ndiff --git a/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/35alicu1i48vig3ohjk62o96m.o b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/35alicu1i48vig3ohjk62o96m.o\nnew file mode 100644\nindex 0000000..fe24db1\nBinary files /dev/null and b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/35alicu1i48vig3ohjk62o96m.o differ\ndiff --git a/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/3qwfb78fmqnca9y7bne4wcseg.o b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/3qwfb78fmqnca9y7bne4wcseg.o\nnew file mode 100644\nindex 0000000..8808a1a\nBinary files /dev/null and b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/3qwfb78fmqnca9y7bne4wcseg.o differ\ndiff --git a/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/48b7dfstvufyj6jrrvgl93gzp.o b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/48b7dfstvufyj6jrrvgl93gzp.o\nnew file mode 100644\nindex 0000000..d68e610\nBinary files /dev/null and b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/48b7dfstvufyj6jrrvgl93gzp.o differ\ndiff --git a/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/4hw3nxauc4gvsju8uo84n1ru5.o b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/4hw3nxauc4gvsju8uo84n1ru5.o\nnew file mode 100644\nindex 0000000..6532285\nBinary files /dev/null and b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/4hw3nxauc4gvsju8uo84n1ru5.o differ\ndiff --git a/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/4ilzf9er5r1j5cahemi7mzg2f.o b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/4ilzf9er5r1j5cahemi7mzg2f.o\nnew file mode 100644\nindex 0000000..584f217\nBinary files /dev/null and b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/4ilzf9er5r1j5cahemi7mzg2f.o differ\ndiff --git a/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/4jpnm423iu7ea2dephxi9vopg.o b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/4jpnm423iu7ea2dephxi9vopg.o\nnew file mode 100644\nindex 0000000..57cb6cb\nBinary files /dev/null and b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/4jpnm423iu7ea2dephxi9vopg.o differ\ndiff --git a/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/4kv59765g0f6yyyhj86ruj4gw.o b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/4kv59765g0f6yyyhj86ruj4gw.o\nnew file mode 100644\nindex 0000000..aba6353\nBinary files /dev/null and b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/4kv59765g0f6yyyhj86ruj4gw.o differ\ndiff --git a/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/4o5rjm55fzrl2j64s83f9tawe.o b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/4o5rjm55fzrl2j64s83f9tawe.o\nnew file mode 100644\nindex 0000000..5a9e5a4\nBinary files /dev/null and b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/4o5rjm55fzrl2j64s83f9tawe.o differ\ndiff --git a/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/4woujox3y9mr86papjruygzgo.o b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/4woujox3y9mr86papjruygzgo.o\nnew file mode 100644\nindex 0000000..b9042e0\nBinary files /dev/null and b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/4woujox3y9mr86papjruygzgo.o differ\ndiff --git a/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/579469m1g1bk6lahm1s9jla13.o b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/579469m1g1bk6lahm1s9jla13.o\nnew file mode 100644\nindex 0000000..0d03629\nBinary files /dev/null and b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/579469m1g1bk6lahm1s9jla13.o differ\ndiff --git a/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/5bvizxomvgr4ks1c99tfu7b53.o b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/5bvizxomvgr4ks1c99tfu7b53.o\nnew file mode 100644\nindex 0000000..cdf1523\nBinary files /dev/null and b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/5bvizxomvgr4ks1c99tfu7b53.o differ\ndiff --git a/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/5mrzs3i31he31u775yf485l2m.o b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/5mrzs3i31he31u775yf485l2m.o\nnew file mode 100644\nindex 0000000..e977e26\nBinary files /dev/null and b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/5mrzs3i31he31u775yf485l2m.o differ\ndiff --git a/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/5oqgozh34u21orpgfa0112bso.o b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/5oqgozh34u21orpgfa0112bso.o\nnew file mode 100644\nindex 0000000..9a1d16e\nBinary files /dev/null and b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/5oqgozh34u21orpgfa0112bso.o differ\ndiff --git a/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/649j6g867at6h8w2wkm5qy1k8.o b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/649j6g867at6h8w2wkm5qy1k8.o\nnew file mode 100644\nindex 0000000..cc503f6\nBinary files /dev/null and b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/649j6g867at6h8w2wkm5qy1k8.o differ\ndiff --git a/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/6kixs302rk1m8q4eifb6dpezl.o b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/6kixs302rk1m8q4eifb6dpezl.o\nnew file mode 100644\nindex 0000000..0c61016\nBinary files /dev/null and b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/6kixs302rk1m8q4eifb6dpezl.o differ\ndiff --git a/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/6nmmiojhdka49r6krkspkhvwp.o b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/6nmmiojhdka49r6krkspkhvwp.o\nnew file mode 100644\nindex 0000000..1716705\nBinary files /dev/null and b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/6nmmiojhdka49r6krkspkhvwp.o differ\ndiff --git a/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/71m74bvlq8etgasfxtd6zc38b.o b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/71m74bvlq8etgasfxtd6zc38b.o\nnew file mode 100644\nindex 0000000..dc373b2\nBinary files /dev/null and b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/71m74bvlq8etgasfxtd6zc38b.o differ\ndiff --git a/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/73dprb3x3dkjchzeidusr13xx.o b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/73dprb3x3dkjchzeidusr13xx.o\nnew file mode 100644\nindex 0000000..2dbe0b8\nBinary files /dev/null and b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/73dprb3x3dkjchzeidusr13xx.o differ\ndiff --git a/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/75zgrvc3t4i10s3jmeh54zhud.o b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/75zgrvc3t4i10s3jmeh54zhud.o\nnew file mode 100644\nindex 0000000..7f91e8f\nBinary files /dev/null and b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/75zgrvc3t4i10s3jmeh54zhud.o differ\ndiff --git a/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/86ivraka229xicw79qs526qtd.o b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/86ivraka229xicw79qs526qtd.o\nnew file mode 100644\nindex 0000000..f231fb3\nBinary files /dev/null and b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/86ivraka229xicw79qs526qtd.o differ\ndiff --git a/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/8a7xblvcbwz63rhc3bu8riwko.o b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/8a7xblvcbwz63rhc3bu8riwko.o\nnew file mode 100644\nindex 0000000..089fbe0\nBinary files /dev/null and b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/8a7xblvcbwz63rhc3bu8riwko.o differ\ndiff --git a/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/90yxc6c77gz26uifamtjje5dy.o b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/90yxc6c77gz26uifamtjje5dy.o\nnew file mode 100644\nindex 0000000..f846787\nBinary files /dev/null and b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/90yxc6c77gz26uifamtjje5dy.o differ\ndiff --git a/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/9aixei938byf4eujgcafrsw4l.o b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/9aixei938byf4eujgcafrsw4l.o\nnew file mode 100644\nindex 0000000..c5dd59d\nBinary files /dev/null and b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/9aixei938byf4eujgcafrsw4l.o differ\ndiff --git a/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/9e8id23q8o9xdlaghdj57whru.o b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/9e8id23q8o9xdlaghdj57whru.o\nnew file mode 100644\nindex 0000000..4f6d5c2\nBinary files /dev/null and b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/9e8id23q8o9xdlaghdj57whru.o differ\ndiff --git a/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/absk6bdss17au6lnog78sswrx.o b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/absk6bdss17au6lnog78sswrx.o\nnew file mode 100644\nindex 0000000..bea4a2c\nBinary files /dev/null and b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/absk6bdss17au6lnog78sswrx.o differ\ndiff --git a/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/af1d6wabdk82f1gqt0nlnj9kj.o b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/af1d6wabdk82f1gqt0nlnj9kj.o\nnew file mode 100644\nindex 0000000..c6070e9\nBinary files /dev/null and b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/af1d6wabdk82f1gqt0nlnj9kj.o differ\ndiff --git a/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/apwupyvvotb10f2jus9lm3ls1.o b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/apwupyvvotb10f2jus9lm3ls1.o\nnew file mode 100644\nindex 0000000..a64f62e\nBinary files /dev/null and b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/apwupyvvotb10f2jus9lm3ls1.o differ\ndiff --git a/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/avu334gdtdg1l6jk8k5kiajgp.o b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/avu334gdtdg1l6jk8k5kiajgp.o\nnew file mode 100644\nindex 0000000..bbe883b\nBinary files /dev/null and b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/avu334gdtdg1l6jk8k5kiajgp.o differ\ndiff --git a/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/bfg6f4tt262a61oil5s6tnyip.o b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/bfg6f4tt262a61oil5s6tnyip.o\nnew file mode 100644\nindex 0000000..30bbb9a\nBinary files /dev/null and b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/bfg6f4tt262a61oil5s6tnyip.o differ\ndiff --git a/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/bmk8p2qrxm3ggowm1zs93iove.o b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/bmk8p2qrxm3ggowm1zs93iove.o\nnew file mode 100644\nindex 0000000..301ecb0\nBinary files /dev/null and b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/bmk8p2qrxm3ggowm1zs93iove.o differ\ndiff --git a/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/bphw8szla9b7ti0s1233nb1pn.o b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/bphw8szla9b7ti0s1233nb1pn.o\nnew file mode 100644\nindex 0000000..0ec01dd\nBinary files /dev/null and b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/bphw8szla9b7ti0s1233nb1pn.o differ\ndiff --git a/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/c7i0blz356licnxvbjjx5456o.o b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/c7i0blz356licnxvbjjx5456o.o\nnew file mode 100644\nindex 0000000..3600fdd\nBinary files /dev/null and b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/c7i0blz356licnxvbjjx5456o.o differ\ndiff --git a/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/cgr8wz05di9bqf8mm7uelid1q.o b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/cgr8wz05di9bqf8mm7uelid1q.o\nnew file mode 100644\nindex 0000000..e6ec2cc\nBinary files /dev/null and b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/cgr8wz05di9bqf8mm7uelid1q.o differ\ndiff --git a/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/cjqc7unygcejdwizqugx6hl5o.o b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/cjqc7unygcejdwizqugx6hl5o.o\nnew file mode 100644\nindex 0000000..fce5d1c\nBinary files /dev/null and b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/cjqc7unygcejdwizqugx6hl5o.o differ\ndiff --git a/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/cm6uqrek01iyac6jaghmxia3l.o b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/cm6uqrek01iyac6jaghmxia3l.o\nnew file mode 100644\nindex 0000000..6efd5ab\nBinary files /dev/null and b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/cm6uqrek01iyac6jaghmxia3l.o differ\ndiff --git a/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/cqpljyduww78dpuzndiwt5gqv.o b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/cqpljyduww78dpuzndiwt5gqv.o\nnew file mode 100644\nindex 0000000..1c43c6c\nBinary files /dev/null and b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/cqpljyduww78dpuzndiwt5gqv.o differ\ndiff --git a/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/cz75h5o5ma8pc48eqmp0zz3wc.o b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/cz75h5o5ma8pc48eqmp0zz3wc.o\nnew file mode 100644\nindex 0000000..ceec20c\nBinary files /dev/null and b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/cz75h5o5ma8pc48eqmp0zz3wc.o differ\ndiff --git a/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/dep-graph.bin b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/dep-graph.bin\nnew file mode 100644\nindex 0000000..b18f943\nBinary files /dev/null and b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/dep-graph.bin differ\ndiff --git a/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/dj5dp7mm298n11063d81hqb5w.o b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/dj5dp7mm298n11063d81hqb5w.o\nnew file mode 100644\nindex 0000000..8e414e2\nBinary files /dev/null and b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/dj5dp7mm298n11063d81hqb5w.o differ\ndiff --git a/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/eillsc0jk2a4kbcjh74ib4uir.o b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/eillsc0jk2a4kbcjh74ib4uir.o\nnew file mode 100644\nindex 0000000..d374413\nBinary files /dev/null and b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/eillsc0jk2a4kbcjh74ib4uir.o differ\ndiff --git a/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/eo3qkvtyvreksf5ep4ew93rqo.o b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/eo3qkvtyvreksf5ep4ew93rqo.o\nnew file mode 100644\nindex 0000000..e0f2891\nBinary files /dev/null and b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/eo3qkvtyvreksf5ep4ew93rqo.o differ\ndiff --git a/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/er94a529lug2ga4ov2mjle72a.o b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/er94a529lug2ga4ov2mjle72a.o\nnew file mode 100644\nindex 0000000..32579b1\nBinary files /dev/null and b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/er94a529lug2ga4ov2mjle72a.o differ\ndiff --git a/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/f2l9pkn9tr614s8k22gai89ro.o b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/f2l9pkn9tr614s8k22gai89ro.o\nnew file mode 100644\nindex 0000000..539179d\nBinary files /dev/null and b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/f2l9pkn9tr614s8k22gai89ro.o differ\ndiff --git a/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/query-cache.bin b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/query-cache.bin\nnew file mode 100644\nindex 0000000..7c65204\nBinary files /dev/null and b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/query-cache.bin differ\ndiff --git a/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/work-products.bin b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/work-products.bin\nnew file mode 100644\nindex 0000000..9d0b983\nBinary files /dev/null and b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy-9xc03b6ry9yklttoqkam1vcya/work-products.bin differ\ndiff --git a/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy.lock b/target/debug/incremental/macros-3mch01uqvpj9m/s-h77dn4hd2l-03wswxy.lock\nnew file mode 100644\nindex 0000000..e69de29\n", "proposed_model_patches": [], "eval_result": "unresolved", "success": true}