{"instance_id": "rust__alphametics", "model_name_or_path": "20250510_021328--claude-3-5-sonnet-20241022_0", "model_patch": "diff --git a/src/lib.rs b/src/lib.rs\nindex a7eb2c2..c4dfbf1 100644\n--- a/src/lib.rs\n+++ b/src/lib.rs\n@@ -1,5 +1,133 @@\n-use std::collections::HashMap;\n+use std::collections::{HashMap, HashSet};\n \n+/// Solves an alphametics puzzle provided in the form \"WORD1 + WORD2 = RESULT\".\n+/// It returns a mapping from char to digit if a solution is found.\n+///\n+/// The input should be a string containing an equation, for example \"SEND + MORE = MONEY\".\n+/// The function returns None if no solution exists.\n pub fn solve(input: &str) -> Option<HashMap<char, u8>> {\n-    todo!(\"Solve the alphametic {input:?}\")\n+    // Remove spaces and split the equation into LHS and RHS\n+    let input = input.replace(\" \", \"\");\n+    let parts: Vec<&str> = input.split('=').collect();\n+    if parts.len() != 2 {\n+        return None;\n+    }\n+    \n+    // Left-hand side might contain multiple addends, split by '+'\n+    let lhs: Vec<&str> = parts[0].split('+').collect();\n+    let rhs = parts[1];\n+\n+    // Collect all unique letters\n+    let mut letters = HashSet::new();\n+    for word in lhs.iter().chain(std::iter::once(&rhs)) {\n+        for ch in word.chars() {\n+            if ch.is_alphabetic() {\n+                letters.insert(ch);\n+            }\n+        }\n+    }\n+    \n+    // If there are more than 10 distinct letters, no solution is possible.\n+    if letters.len() > 10 {\n+        return None;\n+    }\n+\n+    // Create a vector of letters for ordering\n+    let letters: Vec<char> = letters.into_iter().collect();\n+\n+    // Create a set of letters that cannot be zero (leading letters of any multi-digit number)\n+    let mut non_zero: HashSet<char> = HashSet::new();\n+    for word in lhs.iter().chain(std::iter::once(&rhs)) {\n+        if word.len() > 1 {\n+            if let Some(first) = word.chars().next() {\n+                non_zero.insert(first);\n+            }\n+        }\n+    }\n+\n+    // Helper function to compute the value of a word given an assignment\n+    fn word_value(word: &str, mapping: &HashMap<char, u8>) -> u64 {\n+        let mut value = 0u64;\n+        for ch in word.chars() {\n+            value = value * 10 + (*mapping.get(&ch).unwrap() as u64);\n+        }\n+        value\n+    }\n+\n+    // Recursive backtracking function to assign digits to letters\n+    fn backtrack(\n+        idx: usize,\n+        letters: &Vec<char>,\n+        mapping: &mut HashMap<char, u8>,\n+        used: &mut [bool; 10],\n+        non_zero: &HashSet<char>,\n+        lhs: &Vec<&str>,\n+        rhs: &str,\n+    ) -> Option<HashMap<char, u8>> {\n+        if idx == letters.len() {\n+            // Once all letters are assigned, evaluate the sum\n+            let sum_lhs: u64 = lhs.iter().map(|word| word_value(word, mapping)).sum();\n+            let value_rhs = word_value(rhs, mapping);\n+            if sum_lhs == value_rhs {\n+                return Some(mapping.clone());\n+            } else {\n+                return None;\n+            }\n+        }\n+        \n+        let letter = letters[idx];\n+        for digit in 0..10 {\n+            if used[digit as usize] {\n+                continue;\n+            }\n+            // Leading letter cannot be zero\n+            if digit == 0 && non_zero.contains(&letter) {\n+                continue;\n+            }\n+            // Choose this digit for the letter\n+            mapping.insert(letter, digit);\n+            used[digit as usize] = true;\n+            \n+            if let Some(result) = backtrack(idx + 1, letters, mapping, used, non_zero, lhs, rhs) {\n+                return Some(result);\n+            }\n+            \n+            // Backtrack\n+            used[digit as usize] = false;\n+            mapping.remove(&letter);\n+        }\n+        None\n+    }\n+\n+    let mut mapping = HashMap::new();\n+    let mut used = [false; 10];\n+    backtrack(0, &letters, &mut mapping, &mut used, &non_zero, &lhs, rhs)\n+}\n+\n+#[cfg(test)]\n+mod tests {\n+    use super::*;\n+\n+    #[test]\n+    fn test_send_more_money() {\n+        let input = \"SEND + MORE = MONEY\";\n+        let solution = solve(input);\n+        assert!(solution.is_some());\n+        let sol = solution.unwrap();\n+        // The canonical solution is: {S: 9, E: 5, N: 6, D: 7, M: 1, O: 0, R: 8, Y: 2}\n+        let expected = vec![('S', 9), ('E', 5), ('N', 6), ('D', 7), ('M', 1), ('O', 0), ('R', 8), ('Y', 2)]\n+            .into_iter()\n+            .collect::<HashMap<_, _>>();\n+        for (k, v) in expected.iter() {\n+            assert_eq!(sol.get(k), Some(v));\n+        }\n+    }\n+\n+    #[test]\n+    fn test_no_solution() {\n+        // Provide an equation that is impossible\n+        let input = \"THIS + IS = IMPOSSIBLE\";\n+        let solution = solve(input);\n+        assert!(solution.is_none());\n+    }\n }\n", "proposed_model_patches": [], "eval_result": "unresolved", "success": true}