{"instance_id": "rust__dot-dsl", "model_name_or_path": "20250510_021328--claude-3-5-sonnet-20241022_0", "model_patch": "diff --git a/src/lib.rs b/src/lib.rs\nindex 3f2514d..39db634 100644\n--- a/src/lib.rs\n+++ b/src/lib.rs\n@@ -1,9 +1,180 @@\n+use std::collections::HashMap;\n+\n+/// This module provides a DSL for building graphs using the builder pattern.\n+/// It allows fluent construction of graph structures, nodes, and edges with attributes.\n pub mod graph {\n-    pub struct Graph;\n+    use super::*;\n+\n+    /// The main Graph struct containing graph attributes, nodes, and edges.\n+    #[derive(Debug)]\n+    pub struct Graph {\n+        pub graph_attrs: HashMap<String, String>,\n+        pub nodes: Vec<Node>,\n+        pub edges: Vec<Edge>,\n+    }\n+\n+    /// A Node in the graph, identified by an id and a set of attributes.\n+    #[derive(Debug)]\n+    pub struct Node {\n+        pub id: String,\n+        pub attrs: HashMap<String, String>,\n+    }\n+\n+    /// An Edge in the graph connecting two nodes with its own attributes.\n+    #[derive(Debug)]\n+    pub struct Edge {\n+        pub from: String,\n+        pub to: String,\n+        pub attrs: HashMap<String, String>,\n+    }\n+\n+    /// Builder for a Graph struct. Provides fluent API to add graph attributes, nodes, and edges.\n+    pub struct GraphBuilder {\n+        graph_attrs: HashMap<String, String>,\n+        nodes: Vec<Node>,\n+        edges: Vec<Edge>,\n+    }\n \n     impl Graph {\n-        pub fn new() -> Self {\n-            todo!(\"Construct a new Graph struct.\");\n+        /// Start building a new Graph using the builder pattern.\n+        pub fn builder() -> GraphBuilder {\n+            GraphBuilder {\n+                graph_attrs: HashMap::new(),\n+                nodes: Vec::new(),\n+                edges: Vec::new(),\n+            }\n+        }\n+    }\n+\n+    impl GraphBuilder {\n+        /// Add or update a graph-level attribute.\n+        pub fn graph_attr(mut self, key: &str, value: &str) -> Self {\n+            self.graph_attrs.insert(key.to_string(), value.to_string());\n+            self\n+        }\n+\n+        /// Begin adding a new node with the given id. Returns a NodeBuilder to allow attribute configuration.\n+        pub fn add_node(self, id: &str) -> NodeBuilder {\n+            NodeBuilder::new(self, id)\n+        }\n+\n+        /// Begin adding a new edge from one node to another. Returns an EdgeBuilder to allow attribute configuration.\n+        pub fn add_edge(self, from: &str, to: &str) -> EdgeBuilder {\n+            EdgeBuilder::new(self, from, to)\n+        }\n+\n+        /// Finalize the builder and obtain the constructed Graph.\n+        pub fn build(self) -> Graph {\n+            Graph {\n+                graph_attrs: self.graph_attrs,\n+                nodes: self.nodes,\n+                edges: self.edges,\n+            }\n+        }\n+    }\n+\n+    /// Builder for a Node. Allows setting attributes and finishing to return to the GraphBuilder.\n+    pub struct NodeBuilder {\n+        graph_builder: GraphBuilder,\n+        id: String,\n+        attrs: HashMap<String, String>,\n+    }\n+\n+    impl NodeBuilder {\n+        fn new(graph_builder: GraphBuilder, id: &str) -> Self {\n+            NodeBuilder {\n+                graph_builder,\n+                id: id.to_string(),\n+                attrs: HashMap::new(),\n+            }\n+        }\n+\n+        /// Add or update an attribute for this node.\n+        pub fn attr(mut self, key: &str, value: &str) -> Self {\n+            self.attrs.insert(key.to_string(), value.to_string());\n+            self\n+        }\n+\n+        /// Finish building the node and return control to the GraphBuilder.\n+        pub fn finish(mut self) -> GraphBuilder {\n+            let node = Node {\n+                id: self.id,\n+                attrs: self.attrs,\n+            };\n+            self.graph_builder.nodes.push(node);\n+            self.graph_builder\n+        }\n+    }\n+\n+    /// Builder for an Edge. Allows setting attributes and finishing to return to the GraphBuilder.\n+    pub struct EdgeBuilder {\n+        graph_builder: GraphBuilder,\n+        from: String,\n+        to: String,\n+        attrs: HashMap<String, String>,\n+    }\n+\n+    impl EdgeBuilder {\n+        fn new(graph_builder: GraphBuilder, from: &str, to: &str) -> Self {\n+            EdgeBuilder {\n+                graph_builder,\n+                from: from.to_string(),\n+                to: to.to_string(),\n+                attrs: HashMap::new(),\n+            }\n+        }\n+\n+        /// Add or update an attribute for this edge.\n+        pub fn attr(mut self, key: &str, value: &str) -> Self {\n+            self.attrs.insert(key.to_string(), value.to_string());\n+            self\n+        }\n+\n+        /// Finish building the edge and return control to the GraphBuilder.\n+        pub fn finish(mut self) -> GraphBuilder {\n+            let edge = Edge {\n+                from: self.from,\n+                to: self.to,\n+                attrs: self.attrs,\n+            };\n+            self.graph_builder.edges.push(edge);\n+            self.graph_builder\n+        }\n+    }\n+\n+    #[cfg(test)]\n+    mod tests {\n+        use super::*;\n+\n+        #[test]\n+        fn build_graph_example() {\n+            // Example DSL similar to Graphviz Dot\n+            // graph {\n+            //     graph [bgcolor=\"yellow\"]\n+            //     a [color=\"red\"]\n+            //     b [color=\"blue\"]\n+            //     a -- b [color=\"green\"]\n+            // }\n+            let graph = Graph::builder()\n+                .graph_attr(\"bgcolor\", \"yellow\")\n+                .add_node(\"a\")\n+                    .attr(\"color\", \"red\")\n+                    .finish()\n+                .add_node(\"b\")\n+                    .attr(\"color\", \"blue\")\n+                    .finish()\n+                .add_edge(\"a\", \"b\")\n+                    .attr(\"color\", \"green\")\n+                    .finish()\n+                .build();\n+\n+            // Check that attributes are correctly set\n+            assert_eq!(graph.graph_attrs.get(\"bgcolor\").unwrap(), \"yellow\");\n+            assert_eq!(graph.nodes.len(), 2);\n+            assert_eq!(graph.edges.len(), 1);\n+            \n+            // Print the graph for debugging; in a real scenario, we might add more logic.\n+            println!(\"Graph: {:?}\", graph);\n         }\n     }\n }\n", "proposed_model_patches": [], "eval_result": "unresolved", "success": true}