{"instance_id": "python__phone-number", "model_name_or_path": "20250510_021328--claude-3-5-sonnet-20241022_0", "model_patch": "diff --git a/phone_number.py b/phone_number.py\nindex ff5b904..5f21c9c 100644\n--- a/phone_number.py\n+++ b/phone_number.py\n@@ -1,3 +1,46 @@\n class PhoneNumber:\n     def __init__(self, number):\n-        pass\n+        # Allowed punctuation characters\n+        allowed_punct = set(\" ()-.+\")\n+        # Check each character\n+        for char in number:\n+            if char.isalpha():\n+                raise ValueError(\"letters not permitted\")\n+            # If char is not digit and not in allowed set,\n+            # It's an unrecognized punctuation\n+            if not char.isdigit() and char not in allowed_punct:\n+                raise ValueError(\"punctuations not permitted\")\n+\n+        # Remove all allowed non-digit characters\n+        digits = ''.join(ch for ch in number if ch.isdigit())\n+\n+        # Validate length\n+        if len(digits) < 10:\n+            raise ValueError(\"must not be fewer than 10 digits\")\n+        if len(digits) > 11:\n+            raise ValueError(\"must not be greater than 11 digits\")\n+        \n+        # If 11 digits, must start with '1'\n+        if len(digits) == 11:\n+            if digits[0] != '1':\n+                raise ValueError(\"11 digits must start with 1\")\n+            # Remove the country code\n+            digits = digits[1:]\n+\n+        # Now digits should be exactly 10 digits\n+        # Validate area code and exchange code\n+        # area code: first 3 digits, exchange: next 3 digits\n+        if digits[0] == '0':\n+            raise ValueError(\"area code cannot start with zero\")\n+        if digits[0] == '1':\n+            raise ValueError(\"area code cannot start with one\")\n+        if digits[3] == '0':\n+            raise ValueError(\"exchange code cannot start with zero\")\n+        if digits[3] == '1':\n+            raise ValueError(\"exchange code cannot start with one\")\n+\n+        self.number = digits\n+\n+    def pretty(self):\n+        # Format: (area) exchange-subscriber\n+        return f\"({self.number[:3]}) {self.number[3:6]}-{self.number[6:]}\"\n", "proposed_model_patches": [], "eval_result": "unresolved", "success": true}