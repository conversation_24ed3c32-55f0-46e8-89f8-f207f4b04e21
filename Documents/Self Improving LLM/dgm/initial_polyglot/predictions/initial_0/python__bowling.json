{"instance_id": "python__bowling", "model_name_or_path": "20250510_021328--claude-3-5-sonnet-20241022_0", "model_patch": "diff --git a/bowling.py b/bowling.py\nindex de2dbb0..b8d2416 100644\n--- a/bowling.py\n+++ b/bowling.py\n@@ -1,9 +1,154 @@\n class BowlingGame:\n     def __init__(self):\n-        pass\n+        self.rolls = []\n \n     def roll(self, pins):\n-        pass\n+        # Validate pins type and value\n+        if not isinstance(pins, int) or pins < 0 or pins > 10:\n+            raise ValueError(\"invalid number of pins\")\n+\n+        if self._is_game_over():\n+            raise IndexError(\"cannot roll after game is over\")\n+\n+        # Determine current frame state to enforce frame-specific rules\n+        curr_frame, ball, prev = self._current_frame()\n+\n+        # For frames 1-9, if this is the second roll, the sum must not exceed 10\n+        if curr_frame < 10 and ball == 2:\n+            if prev is not None and prev + pins > 10:\n+                raise ValueError(\"Pin count exceeds 10 in a frame\")\n+\n+        # For 10th frame, if this is the second roll and the first roll wasn't a strike,\n+        # ensure the sum of the first two rolls does not exceed 10\n+        if curr_frame == 10 and ball == 2:\n+            tenth = self._tenth_frame_rolls()\n+            if len(tenth) == 1 and tenth[0] != 10 and tenth[0] + pins > 10:\n+                raise ValueError(\"Pin count exceeds 10 in a frame\")\n+\n+        # No additional constraints for bonus rolls in 10th frame\n+\n+        self.rolls.append(pins)\n \n     def score(self):\n-        pass\n+        if not self._is_game_over():\n+            raise Exception(\"Score cannot be taken until the end of the game\")\n+\n+        total = 0\n+        roll_index = 0\n+        for frame in range(10):\n+            # Strike case\n+            if self.rolls[roll_index] == 10:\n+                total += 10 + self.rolls[roll_index + 1] + self.rolls[roll_index + 2]\n+                roll_index += 1\n+            else:\n+                frame_sum = self.rolls[roll_index] + self.rolls[roll_index + 1]\n+                if frame_sum == 10:  # spare\n+                    total += 10 + self.rolls[roll_index + 2]\n+                else:\n+                    total += frame_sum\n+                roll_index += 2\n+        return total\n+\n+    def _is_game_over(self):\n+        \"\"\"\n+        Simulate the progression of frames. Return True if 10 frames have been completed\n+        according to bowling rules.\n+        \"\"\"\n+        i = 0\n+        frame = 0\n+        rolls = self.rolls\n+        n = len(rolls)\n+\n+        # Frames 1 to 9\n+        while frame < 9 and i < n:\n+            # Strike: one roll frame\n+            if rolls[i] == 10:\n+                i += 1\n+                frame += 1\n+            else:\n+                if i + 1 >= n:\n+                    return False\n+                # Validate that the frame's roll sum does not exceed 10, though this should have been caught\n+                if rolls[i] + rolls[i + 1] > 10:\n+                    raise ValueError(\"Pin count exceeds 10 in a frame\")\n+                i += 2\n+                frame += 1\n+\n+        # Tenth frame\n+        if frame < 9:\n+            return False  # Should not happen\n+        remaining = rolls[i:]\n+        if len(remaining) < 2:\n+            return False\n+        # If strike or spare in tenth, then expect a third ball\n+        if remaining[0] == 10 or (remaining[0] + remaining[1] == 10):\n+            return len(remaining) == 3\n+        else:\n+            return len(remaining) == 2\n+\n+    def _current_frame(self):\n+        \"\"\"\n+        Returns a tuple (frame_number, ball_number, previous_roll) for the current incomplete frame.\n+        For frames 1-9, previous_roll is provided if the first ball has been thrown.\n+        For the tenth frame, previous_roll is provided on the second ball if needed.\n+        \"\"\"\n+        i = 0\n+        frame = 1\n+        rolls = self.rolls\n+        n = len(rolls)\n+\n+        # Process frames 1 to 9\n+        while frame < 10 and i < n:\n+            if rolls[i] == 10:\n+                i += 1\n+                frame += 1\n+            else:\n+                if i + 1 < n:\n+                    i += 2\n+                    frame += 1\n+                else:\n+                    # Incomplete frame, ball 2 expected\n+                    return (frame, 2, rolls[i])\n+\n+        # Tenth frame\n+        tenth = rolls[i:]\n+        if len(tenth) == 0:\n+            return (10, 1, None)\n+        elif len(tenth) == 1:\n+            return (10, 2, tenth[0])\n+        elif len(tenth) == 2:\n+            # Only allow a third ball if the frame is a strike or a spare\n+            if tenth[0] == 10 or (tenth[0] + tenth[1] == 10):\n+                return (10, 3, None)\n+            else:\n+                # Frame complete\n+                return (10, 3, None)  # Though game is over; _is_game_over will catch this\n+        else:\n+            # More than 3 rolls in the tenth means game over\n+            return (10, 4, None)\n+\n+    def _tenth_frame_rolls(self):\n+        \"\"\"\n+        Returns the list of rolls in the tenth frame.\n+        \"\"\"\n+        i = 0\n+        frame = 1\n+        rolls = self.rolls\n+        n = len(rolls)\n+        # Process frames 1 to 9\n+        while frame < 10 and i < n:\n+            if rolls[i] == 10:\n+                i += 1\n+            else:\n+                i += 2\n+            frame += 1\n+        return rolls[i:]\n+\n+\n+# For manual testing, you can uncomment the following\n+# if __name__ == '__main__':\n+#     game = BowlingGame()\n+#     # A perfect game example\n+#     for _ in range(12):\n+#         game.roll(10)\n+#     print(game.score())  # Should print 300\n", "proposed_model_patches": [], "eval_result": "unresolved", "success": true}