{"instance_id": "rust__grade-school", "model_name_or_path": "20250510_021328--claude-3-5-sonnet-20241022_0", "model_patch": "diff --git a/src/lib.rs b/src/lib.rs\nindex da3f536..bdb67a8 100644\n--- a/src/lib.rs\n+++ b/src/lib.rs\n@@ -1,23 +1,37 @@\n-pub struct School {}\n+use std::collections::{BTreeMap, BTreeSet};\n+\n+pub struct School {\n+    roster: BTreeMap<u32, BTreeSet<String>>,\n+}\n \n impl School {\n     pub fn new() -> School {\n-        todo!()\n+        School {\n+            roster: BTreeMap::new(),\n+        }\n     }\n \n+    // Adds a student to a grade. Panics if the student is already in the roster.\n     pub fn add(&mut self, grade: u32, student: &str) {\n-        todo!(\"Add {student} to the roster for {grade}\")\n+        // Check if student already exists in any grade\n+        for (_g, students) in self.roster.iter() {\n+            if students.contains(student) {\n+                panic!(\"Student '{}' is already added to the roster\", student);\n+            }\n+        }\n+        self.roster.entry(grade).or_insert(BTreeSet::new()).insert(student.to_string());\n     }\n \n+    // Returns a vector of all grades that have at least one student, sorted in ascending order\n     pub fn grades(&self) -> Vec<u32> {\n-        todo!()\n+        self.roster.keys().cloned().collect()\n     }\n \n-    // If `grade` returned a reference, `School` would be forced to keep a `Vec<String>`\n-    // internally to lend out. By returning an owned vector of owned `String`s instead,\n-    // the internal structure can be completely arbitrary. The tradeoff is that some data\n-    // must be copied each time `grade` is called.\n+    // Returns all students in a given grade, sorted alphabetically. If no such grade exists, returns an empty vector.\n     pub fn grade(&self, grade: u32) -> Vec<String> {\n-        todo!(\"Return the list of students in {grade}\")\n+        match self.roster.get(&grade) {\n+            Some(students) => students.iter().cloned().collect(),\n+            None => Vec::new(),\n+        }\n     }\n }\n", "proposed_model_patches": [], "eval_result": "unresolved", "success": true}