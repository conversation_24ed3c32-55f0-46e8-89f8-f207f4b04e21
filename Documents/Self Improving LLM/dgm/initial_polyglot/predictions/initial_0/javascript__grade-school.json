{"instance_id": "javascript__grade-school", "model_name_or_path": "20250510_021328--claude-3-5-sonnet-20241022_0", "model_patch": "diff --git a/grade-school.js b/grade-school.js\nindex 6385d96..1637687 100644\n--- a/grade-school.js\n+++ b/grade-school.js\n@@ -1,18 +1,52 @@\n //\n-// This is only a SKELETON file for the 'Grade School' exercise. It's been provided as a\n-// convenience to get you started writing code faster.\n+// Grade School Exercise Solution\n //\n+// This implementation manages a roster of students divided by their grade. \n+// It supports adding a student to a grade, retrieving a list of students in a given grade, \n+// and retrieving a sorted list of all students enrolled in the school.\n+//\n+// Note: Each student is identified by a unique first name and cannot be added more than once.\n \n export class GradeSchool {\n+  constructor() {\n+    // Object mapping grade level to an array of student names\n+    this.rosterData = {};\n+    // Set to track all student names that have been added\n+    this.allStudents = new Set();\n+  }\n+\n+  // Returns an array of all student names in the school sorted by grade (ascending) and by name alphabetically within each grade\n   roster() {\n-    throw new Error('Remove this statement and implement this function');\n+    const grades = Object.keys(this.rosterData).map(Number).sort((a, b) => a - b);\n+    let result = [];\n+    for (const grade of grades) {\n+      // get a copy and sort alphabetically\n+      const students = [...this.rosterData[grade]].sort();\n+      result = result.concat(students);\n+    }\n+    return result;\n   }\n \n-  add() {\n-    throw new Error('Remove this statement and implement this function');\n+  // Adds the given student to the given grade. Throws an error if the student has already been added.\n+  add(student, grade) {\n+    if (this.allStudents.has(student)) {\n+      throw new Error('Student already added');\n+    }\n+    this.allStudents.add(student);\n+    if (!this.rosterData[grade]) {\n+      this.rosterData[grade] = [];\n+    }\n+    this.rosterData[grade].push(student);\n+    // Ensure the students in this grade are always sorted\n+    this.rosterData[grade].sort();\n   }\n \n-  grade() {\n-    throw new Error('Remove this statement and implement this function');\n+  // Returns an array of student names enrolled in the specified grade, sorted alphabetically. Returns an empty array if no students are found for the grade.\n+  grade(grade) {\n+    if (!this.rosterData[grade]) {\n+      return [];\n+    }\n+    // Return a sorted array if not already sorted\n+    return [...this.rosterData[grade]].sort();\n   }\n }\n", "proposed_model_patches": [], "eval_result": "unresolved", "success": true}