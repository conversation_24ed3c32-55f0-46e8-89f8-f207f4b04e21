{"instance_id": "javascript__poker", "model_name_or_path": "20250510_021328--claude-3-5-sonnet-20241022_0", "model_patch": "diff --git a/poker.js b/poker.js\nindex c61fb93..a44a4c8 100644\n--- a/poker.js\n+++ b/poker.js\n@@ -1,8 +1,193 @@\n-//\n-// This is only a SKELETON file for the 'Poker' exercise. It's been provided as a\n-// convenience to get you started writing code faster.\n-//\n+/*\n+Puzzle: Pick the best hand(s) from a list of poker hands.\n \n+Each hand is represented as a string of five cards separated by spaces. Each card is a two-character string where the first character represents the rank\n+('2'-'9', 'T', 'J', 'Q', 'K','A') and the second character represents the suit ('S', 'H', 'D', 'C').\n+\n+The bestHands function should return an array of the winning hand(s). In case of tie, include all the tied hands in the order they appeared.\n+\n+The ranking of hands is as follows (from best to worst):\n+  1. Straight Flush\n+  2. Four of a Kind\n+  3. Full House\n+  4. Flush\n+  5. Straight\n+  6. Three of a Kind\n+  7. Two Pair\n+  8. One Pair\n+  9. High Card\n+\n+For tie-breaking, compare the rank values involved in the combination and then the kickers in descending order.\n+*/\n+\n+// Map for card rank values\n+const RANK_VALUES = {\n+  '2': 2,\n+  '3': 3,\n+  '4': 4,\n+  '5': 5,\n+  '6': 6,\n+  '7': 7,\n+  '8': 8,\n+  '9': 9,\n+  'T': 10,\n+  'J': 11,\n+  'Q': 12,\n+  'K': 13,\n+  'A': 14,\n+};\n+\n+// Evaluate a hand string and return a score tuple for comparison.\n+// The returned tuple will be an array where the first element is the category rank and the remaining elements are tiebreakers.\n+// Higher tuple values indicate a better hand.\n+function evaluateHand(handStr) {\n+  const cards = handStr.split(' ');\n+  const ranks = cards.map(card => RANK_VALUES[card[0]]);\n+  const suits = cards.map(card => card[1]);\n+  \n+  // check flush: all same suit\n+  const isFlush = suits.every(s => s === suits[0]);\n+  \n+  // sort ranks in descending order\n+  let sortedRanks = [...ranks].sort((a,b) => b - a);\n+  \n+  // check straight: sort ascending unique\n+  let sortedAsc = [...ranks].sort((a,b) => a - b);\n+  let isStraight = true;\n+  for (let i = 1; i < sortedAsc.length; i++) {\n+    if (sortedAsc[i] !== sortedAsc[i-1] + 1) {\n+      isStraight = false;\n+      break;\n+    }\n+  }\n+  // special case: Ace can be low in A-2-3-4-5\n+  if (!isStraight && sortedRanks[0] === 14) {\n+    let lowAceRanks = ranks.map(v => v === 14 ? 1 : v);\n+    lowAceRanks.sort((a,b) => b - a);\n+    let ascLow = [...lowAceRanks].sort((a,b) => a - b);\n+    let straightLow = true;\n+    for (let i = 1; i < ascLow.length; i++) {\n+      if (ascLow[i] !== ascLow[i-1] + 1) {\n+        straightLow = false;\n+        break;\n+      }\n+    }\n+    if (straightLow) {\n+      isStraight = true;\n+      // For tie-breaking, the high card is 5 in A-2-3-4-5\n+      sortedRanks = [5,4,3,2,1];\n+    }\n+  }\n+\n+  // Count frequencies of ranks\n+  const freq = {};\n+  for (const r of ranks) {\n+    freq[r] = (freq[r] || 0) + 1;\n+  }\n+\n+  // Create an array of [rank, count]\n+  let groups = Object.entries(freq).map(([r, count]) => [parseInt(r), count]);\n+  // sort by descending count then descending rank\n+  groups.sort((a, b) => {\n+    if(b[1] === a[1]) {\n+      return b[0] - a[0];\n+    }\n+    return b[1] - a[1];\n+  });\n+\n+  // Get just the counts sorted\n+  const counts = groups.map(g => g[1]);\n+  const groupRanks = groups.map(g => g[0]);\n+\n+  // Ranking categories:\n+  // 8: Straight Flush\n+  // 7: Four of a Kind\n+  // 6: Full House\n+  // 5: Flush\n+  // 4: Straight\n+  // 3: Three of a Kind\n+  // 2: Two Pair\n+  // 1: One Pair\n+  // 0: High Card\n+\n+  // Straight Flush\n+  if (isFlush && isStraight) {\n+    // use highest card of the straight as tiebreaker\n+    return [8, sortedRanks[0]];\n+  }\n+\n+  // Four of a Kind\n+  if (counts[0] === 4) {\n+    // groupRanks[0] is quad, groupRanks[1] is kicker\n+    return [7, groupRanks[0], groupRanks[1]];\n+  }\n+\n+  // Full House\n+  if (counts[0] === 3 && counts[1] === 2) {\n+    return [6, groupRanks[0], groupRanks[1]];\n+  }\n+\n+  // Flush\n+  if (isFlush) {\n+    // tiebreaker: sorted ranks descending\n+    return [5, ...sortedRanks];\n+  }\n+\n+  // Straight\n+  if (isStraight) {\n+    return [4, sortedRanks[0]];\n+  }\n+\n+  // Three of a Kind\n+  if (counts[0] === 3) {\n+    // Remove the triplet from sortedRanks\n+    let remaining = sortedRanks.filter(r => r !== groupRanks[0]);\n+    return [3, groupRanks[0], ...remaining];\n+  }\n+\n+  // Two Pair\n+  if (counts[0] === 2 && counts[1] === 2) {\n+    // kicker is the remaining card\n+    let kicker = groupRanks[2];\n+    return [2, groupRanks[0], groupRanks[1], kicker];\n+  }\n+\n+  // One Pair\n+  if (counts[0] === 2) {\n+    let remaining = sortedRanks.filter(r => r !== groupRanks[0]);\n+    return [1, groupRanks[0], ...remaining];\n+  }\n+\n+  // High Card\n+  return [0, ...sortedRanks];\n+}\n+\n+// Compare two score arrays lexicographically\n+function compareScores(scoreA, scoreB) {\n+  for (let i = 0; i < Math.max(scoreA.length, scoreB.length); i++) {\n+    const a = scoreA[i] || 0;\n+    const b = scoreB[i] || 0;\n+    if (a !== b) return a - b;\n+  }\n+  return 0;\n+}\n+\n+// Main function: pick the best hand(s)\n export const bestHands = (hands) => {\n-  throw new Error('Remove this statement and implement this function');\n+  // Evaluate each hand with its score\n+  const scoredHands = hands.map(hand => ({\n+    hand,\n+    score: evaluateHand(hand)\n+  }));\n+\n+  // Find best score\n+  let bestScore = null;\n+  for (const entry of scoredHands) {\n+    if (bestScore === null || compareScores(entry.score, bestScore) > 0) {\n+      bestScore = entry.score;\n+    }\n+  }\n+\n+  // Return all hands that match the best score, preserving input order\n+  return scoredHands.filter(entry => compareScores(entry.score, bestScore) === 0).map(entry => entry.hand);\n };\n", "proposed_model_patches": [], "eval_result": "unresolved", "success": true}