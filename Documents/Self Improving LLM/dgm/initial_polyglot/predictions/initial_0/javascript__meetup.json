{"instance_id": "javascript__meetup", "model_name_or_path": "20250510_021328--claude-3-5-sonnet-20241022_0", "model_patch": "diff --git a/meetup.js b/meetup.js\nindex 1eb17a0..199d51a 100644\n--- a/meetup.js\n+++ b/meetup.js\n@@ -1,8 +1,78 @@\n //\n-// This is only a SKELETON file for the 'Meetup' exercise. It's been provided as a\n-// convenience to get you started writing code faster.\n-//\n+// This is a solution for the 'Meetup' exercise.\n+// Given a year, month, weekday, and schedule, it finds the exact date of the meetup.\n+// Month is provided in 1-indexed form (e.g. January = 1, February = 2, etc.)\n+// Weekday is a string like \"Monday\", \"Tuesday\", etc.\n+// Schedule is one of: 'first', 'second', 'third', 'fourth', 'last', 'teenth'\n+\n+export const meetup = (year, month, weekday, schedule) => {\n+  const weekdayMap = {\n+    'Sunday': 0,\n+    'Monday': 1,\n+    'Tuesday': 2,\n+    'Wednesday': 3,\n+    'Thursday': 4,\n+    'Friday': 5,\n+    'Saturday': 6,\n+  };\n+\n+  const targetDay = weekdayMap[weekday];\n+  if (targetDay === undefined) {\n+    throw new Error(`Invalid weekday: ${weekday}`);\n+  }\n+\n+  // Adjust month index for Date constructor (0-indexed)\n+  const monthIndex = month - 1;\n+\n+  if (schedule === 'teenth') {\n+    // teenth days are 13 to 19\n+    for (let day = 13; day <= 19; day++) {\n+      const date = new Date(year, monthIndex, day);\n+      if (date.getDay() === targetDay) {\n+        return date;\n+      }\n+    }\n+  } else if (schedule === 'first') {\n+    // The first week, check days 1-7\n+    for (let day = 1; day <= 7; day++) {\n+      const date = new Date(year, monthIndex, day);\n+      if (date.getDay() === targetDay) {\n+        return date;\n+      }\n+    }\n+  } else if (schedule === 'second' || schedule === 'third' || schedule === 'fourth') {\n+    // Find the first occurence and then add 7 or 14 or 21 days\n+    let firstOccurrence = null;\n+    for (let day = 1; day <= 7; day++) {\n+      const date = new Date(year, monthIndex, day);\n+      if (date.getDay() === targetDay) {\n+        firstOccurrence = date;\n+        break;\n+      }\n+    }\n+    if (!firstOccurrence) {\n+      throw new Error('Unable to find the first occurrence for the given weekday');\n+    }\n+    let offset = 0;\n+    if (schedule === 'second') {\n+      offset = 7;\n+    } else if (schedule === 'third') {\n+      offset = 14;\n+    } else if (schedule === 'fourth') {\n+      offset = 21;\n+    }\n+    return new Date(firstOccurrence.getFullYear(), firstOccurrence.getMonth(), firstOccurrence.getDate() + offset);\n+  } else if (schedule === 'last') {\n+    // Get last day of the month. new Date(year, month, 0) returns the last day of the previous month if month is 0-indexed, but here month is 1-indexed.\n+    // For instance, for August (month=8), new Date(year, 8, 0) gives the last day of August.\n+    const lastDayOfMonth = new Date(year, month, 0).getDate();\n+    for (let day = lastDayOfMonth; day > lastDayOfMonth - 7; day--) {\n+      const date = new Date(year, monthIndex, day);\n+      if (date.getDay() === targetDay) {\n+        return date;\n+      }\n+    }\n+  }\n \n-export const meetup = () => {\n-  throw new Error('Remove this statement and implement this function');\n+  throw new Error('Invalid schedule or date not found');\n };\n", "proposed_model_patches": [], "eval_result": "unresolved", "success": true}