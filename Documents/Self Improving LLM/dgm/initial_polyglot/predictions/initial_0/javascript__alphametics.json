{"instance_id": "javascript__alphametics", "model_name_or_path": "20250510_021328--claude-3-5-sonnet-20241022_0", "model_patch": "diff --git a/alphametics.js b/alphametics.js\nindex fd5babd..e564dce 100644\n--- a/alphametics.js\n+++ b/alphametics.js\n@@ -1,8 +1,89 @@\n //\n-// This is only a SKELETON file for the 'Alphametics' exercise. It's been provided as a\n-// convenience to get you started writing code faster.\n+// Alphametics solver for puzzles.\n //\n+// The function solve takes an alphametics puzzle string, for example:\n+//   \"SEND + MORE == MONEY\"\n+// It returns an object mapping each letter to its corresponding digit that solves the puzzle.\n+// If no solution exists, it throws an error.\n \n-export const solve = () => {\n-  throw new Error('Remove this statement and implement this function');\n+export const solve = (puzzle) => {\n+  // Remove all whitespace from the puzzle\n+  puzzle = puzzle.replace(/\\s+/g, '');\n+  \n+  // Split puzzle at '==' to obtain left-hand side (addends) and result\n+  const parts = puzzle.split('==');\n+  if (parts.length !== 2) {\n+    throw new Error('Invalid puzzle format. Expected exactly one \"==\" sign.');\n+  }\n+  const left = parts[0];\n+  const right = parts[1];\n+  \n+  // Split the left hand side by '+' to get addends and include the right hand side as the sum result\n+  const addends = left.split('+');\n+  const words = [...addends, right];\n+  \n+  // Extract unique letters\n+  const lettersSet = new Set();\n+  for (const word of words) {\n+    for (const ch of word) {\n+      lettersSet.add(ch);\n+    }\n+  }\n+  const letters = Array.from(lettersSet);\n+  \n+  // Identify letters that cannot be zero (leading digit of a multi-digit number)\n+  const nonZero = new Set();\n+  for (const word of words) {\n+    if (word.length > 1) {\n+      nonZero.add(word[0]);\n+    }\n+  }\n+  \n+  // Helper: Convert a word to its numeric value given a mapping.\n+  const wordValue = (word, mapping) => {\n+    let value = 0;\n+    for (const ch of word) {\n+      value = value * 10 + mapping[ch];\n+    }\n+    return value;\n+  };\n+\n+  let solution = null;\n+\n+  // Backtracking: assign digits to letters recursively\n+  const permute = (pos, mapping, used) => {\n+    if (pos === letters.length) {\n+      // All letters assigned, evaluate the equation\n+      const sumAddends = addends.reduce((acc, word) => acc + wordValue(word, mapping), 0);\n+      const resultValue = wordValue(right, mapping);\n+      if (sumAddends === resultValue) {\n+        solution = { ...mapping };\n+        return true;\n+      }\n+      return false;\n+    }\n+    \n+    for (let d = 0; d < 10; d++) {\n+      if (used[d]) continue;\n+      // Ensure that letters that must not be zero are not assigned 0\n+      if (d === 0 && nonZero.has(letters[pos])) continue;\n+      \n+      mapping[letters[pos]] = d;\n+      used[d] = true;\n+      if (permute(pos + 1, mapping, used)) {\n+        return true;\n+      }\n+      used[d] = false;\n+      delete mapping[letters[pos]];\n+    }\n+    return false;\n+  };\n+\n+  permute(0, {}, Array(10).fill(false));\n+\n+  if (solution === null) {\n+    throw new Error('No solution');\n+  }\n+  \n+  return solution;\n };\n", "proposed_model_patches": [], "eval_result": "unresolved", "success": true}