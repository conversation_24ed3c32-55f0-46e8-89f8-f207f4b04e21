+ set -e
+ '[' '!' -e node_modules ']'
+ ln -s /npm-install/node_modules .
+ '[' '!' -e package-lock.json ']'
+ ln -s /npm-install/package-lock.json .
+ sed -i 's/\bxtest(/test(/g' parallel-letter-frequency.spec.js
+ npm run test

> test
> jest ./*

FAIL ./parallel-letter-frequency.spec.js
  ● Test suite failed to run

    Je<PERSON> encountered an unexpected token

    Jest failed to parse a file. This happens e.g. when your code or its dependencies use non-standard JavaScript syntax, or when Je<PERSON> is not configured to support such syntax.

    Out of the box Jest supports Babel, which will be used to transform your files into valid JS based on your Babel configuration.

    By default "node_modules" folder is ignored by transformers.

    Here's what you can do:
     • If you are trying to use ECMAScript Modules, see https://jestjs.io/docs/ecmascript-modules for how to enable it.
     • If you are trying to use TypeScript, see https://jestjs.io/docs/getting-started#using-typescript
     • To have some of your "node_modules" files transformed, you can specify a custom "transformIgnorePatterns" in your config.
     • If you need a custom transformation specify a "transform" option in your config.
     • If you simply want to mock your non-JS modules (e.g. binary assets) you can stub them out with the "moduleNameMapper" config option.

    You'll find more details and examples of these config options in the docs:
    https://jestjs.io/docs/configuration
    For information about custom transformations, see:
    https://jestjs.io/docs/code-transformation

    Details:

    /testbed/parallel-letter-frequency.js:35
        const worker = new _nodeWorker_threads.Worker(new URL(import.meta.url), {
                                                                     ^^^^

    SyntaxError: Cannot use 'import.meta' outside a module

    > 1 | import { parallelLetterFrequency } from './parallel-letter-frequency';
        | ^
      2 |
      3 | describe('ParallelLetterFrequency', () => {
      4 |   test('no texts', async () => {

      at Runtime.createScriptFromCode (../npm-install/node_modules/jest-runtime/build/index.js:1505:14)
      at Object.require (parallel-letter-frequency.spec.js:1:1)

Test Suites: 1 failed, 1 total
Tests:       0 total
Snapshots:   0 total
Time:        5.991 s
Ran all test suites matching /.\/LICENSE|.\/babel.config.js|.\/eval.sh|.\/node_modules|.\/package-lock.json|.\/package.json|.\/parallel-letter-frequency.js|.\/parallel-letter-frequency.spec.js/i.
npm notice
npm notice New major version of npm available! 10.8.2 -> 11.3.0
npm notice Changelog: https://github.com/npm/cli/releases/tag/v11.3.0
npm notice To update run: npm install -g npm@11.3.0
npm notice
