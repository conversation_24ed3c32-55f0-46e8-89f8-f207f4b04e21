{"instance_id": "python__sgf-parsing", "model_name_or_path": "20250510_021328--claude-3-5-sonnet-20241022_0", "model_patch": "diff --git a/sgf_parsing.py b/sgf_parsing.py\nindex fa30f3f..bf60805 100644\n--- a/sgf_parsing.py\n+++ b/sgf_parsing.py\n@@ -26,4 +26,120 @@ class SgfTree:\n \n \n def parse(input_string):\n-    pass\n+    if not input_string:\n+        raise ValueError(\"tree missing\")\n+\n+    index = 0\n+    length = len(input_string)\n+\n+    def skip_whitespace():\n+        nonlocal index\n+        while index < length and input_string[index].isspace():\n+            index += 1\n+\n+    def parse_tree():\n+        nonlocal index\n+        skip_whitespace()\n+        if index >= length or input_string[index] != '(':\n+            raise ValueError(\"tree missing\")\n+        index += 1  # consume '('\n+        skip_whitespace()\n+        nodes = parse_sequence()\n+        if not nodes:\n+            raise ValueError(\"tree with no nodes\")\n+\n+        skip_whitespace()\n+        # Parse variations (subtrees) if any\n+        variations = []\n+        while index < length and input_string[index] == '(':\n+            child_tree = parse_tree()\n+            variations.append(child_tree)\n+            skip_whitespace()\n+\n+        if index >= length or input_string[index] != ')':\n+            raise ValueError(\"tree missing\")\n+        index += 1  # consume ')'\n+\n+        # Attach any variations to the last node in the sequence\n+        nodes[-1].children.extend(variations)\n+\n+        # Link sequential nodes: each node becomes the child of the previous node\n+        root = nodes[0]\n+        current = root\n+        for node in nodes[1:]:\n+            current.children.append(node)\n+            current = node\n+        return root\n+\n+    def parse_sequence():\n+        nonlocal index\n+        nodes = []\n+        skip_whitespace()\n+        while index < length and input_string[index] == ';':\n+            index += 1  # consume ';'\n+            skip_whitespace()\n+            node = parse_node()\n+            nodes.append(node)\n+            skip_whitespace()\n+        return nodes\n+\n+    def parse_node():\n+        nonlocal index\n+        properties = {}\n+        skip_whitespace()\n+        # Parse property key-value pairs until we hit a node delimiter (';', '(', ')')\n+        while index < length and input_string[index] not in ';()':\n+            if not input_string[index].isalpha():\n+                raise ValueError(\"properties without delimiter\")\n+            # Parse property key: sequence of alphabetic characters\n+            start = index\n+            while index < length and input_string[index].isalpha():\n+                index += 1\n+            key = input_string[start:index]\n+            if key != key.upper():\n+                raise ValueError(\"property must be in uppercase\")\n+            skip_whitespace()\n+            if index >= length or input_string[index] != '[':\n+                raise ValueError(\"properties without delimiter\")\n+            values = []\n+            # A property can have multiple values\n+            while index < length and input_string[index] == '[':\n+                index += 1  # consume '['\n+                value_chars = []\n+                while index < length and input_string[index] != ']':\n+                    if input_string[index] == '\\\\':\n+                        index += 1\n+                        if index < length:\n+                            if input_string[index] == '\\n':\n+                                # If newline immediately after escape, skip it\n+                                index += 1\n+                                continue\n+                            else:\n+                                value_chars.append(input_string[index])\n+                                index += 1\n+                        else:\n+                            break\n+                    else:\n+                        ch = input_string[index]\n+                        # Convert whitespace (except newline) to space\n+                        if ch != '\\n' and ch.isspace():\n+                            ch = ' '\n+                        value_chars.append(ch)\n+                        index += 1\n+                if index >= length or input_string[index] != ']':\n+                    raise ValueError(\"properties without delimiter\")\n+                index += 1  # consume ']'\n+                values.append(''.join(value_chars))\n+                skip_whitespace()\n+            properties[key] = values[0] if len(values) == 1 else values\n+            skip_whitespace()\n+        return SgfTree(properties=properties)\n+\n+    skip_whitespace()\n+    if index >= length or input_string[index] != '(':\n+        raise ValueError(\"tree missing\")\n+    tree = parse_tree()\n+    skip_whitespace()\n+    if index != length:\n+        raise ValueError(\"tree missing\")\n+    return tree\n", "proposed_model_patches": [], "eval_result": "unresolved", "success": true}