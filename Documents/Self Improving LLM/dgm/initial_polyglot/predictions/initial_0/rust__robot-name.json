{"instance_id": "rust__robot-name", "model_name_or_path": "20250510_021328--claude-3-5-sonnet-20241022_0", "model_patch": "diff --git a/src/lib.rs b/src/lib.rs\nindex 37bf158..de0f3b1 100644\n--- a/src/lib.rs\n+++ b/src/lib.rs\n@@ -1,15 +1,100 @@\n-pub struct Robot;\n+use rand::Rng;\n+use std::cell::RefCell;\n+use std::collections::HashSet;\n+use std::sync::Mutex;\n+\n+// We use lazy_static for our global used names store.\n+// While global mutable state is usually discouraged, here it makes sure that no two robots share the same name.\n+\n+#[macro_use]\n+extern crate lazy_static;\n+\n+lazy_static! {\n+    static ref USED_NAMES: Mutex<HashSet<String>> = Mutex::new(HashSet::new());\n+}\n+\n+// Generates a random name in the format: two uppercase letters followed by three digits.\n+fn generate_name() -> String {\n+    let mut rng = rand::thread_rng();\n+    let letters: String = (0..2)\n+        .map(|_| {\n+            let ch = rng.gen_range(b'A', b'Z' + 1) as char;\n+            ch\n+        })\n+        .collect();\n+    let digits: String = (0..3)\n+        .map(|_| {\n+            let d = rng.gen_range(0, 10);\n+            std::char::from_digit(d, 10).unwrap()\n+        })\n+        .collect();\n+    format!(\"{}{}\", letters, digits)\n+}\n+\n+// Loop until a unique name is found. Global MUTEX ensures every name is unique across all robots.\n+fn generate_unique_name() -> String {\n+    loop {\n+        let name = generate_name();\n+        let mut used = USED_NAMES.lock().unwrap();\n+        if !used.contains(&name) {\n+            used.insert(name.clone());\n+            return name;\n+        }\n+    }\n+}\n+\n+// The Robot struct now holds its name in an interior mutable container.\n+// When created, a robot has no name (None). The first time name() is called, a unique name is generated.\n+// Calling reset_name() clears the name (removing it from the global used names), so the next call to name() produces a new unique name.\n+\n+pub struct Robot {\n+    // Using RefCell to allow interior mutability so that name() can be called on an immutable reference.\n+    name: RefCell<Option<String>>,\n+}\n \n impl Robot {\n+    // Creates a new robot with no name assigned.\n     pub fn new() -> Self {\n-        todo!(\"Construct a new Robot struct.\");\n+        Robot { name: RefCell::new(None) }\n     }\n \n-    pub fn name(&self) -> &str {\n-        todo!(\"Return the reference to the robot's name.\");\n+    // Returns the robot's name. If the robot has no name, it gets assigned a new unique name.\n+    // Note: We return an owned String to avoid lifetime issues with borrowing from a RefCell.\n+    pub fn name(&self) -> String {\n+        if self.name.borrow().is_none() {\n+            let new_name = generate_unique_name();\n+            *self.name.borrow_mut() = Some(new_name);\n+        }\n+        // Clone the stored name so that the caller gets an owned copy.\n+        self.name.borrow().as_ref().unwrap().clone()\n     }\n \n+    // Resets the robot's name. The old name is freed (removed from the global used set), and the next call to name() will generate a new one.\n+    // Note: We require a mutable reference here as we're logically modifying the robot's state.\n     pub fn reset_name(&mut self) {\n-        todo!(\"Assign a new unique name to the robot.\");\n+        if let Some(old_name) = self.name.borrow_mut().take() {\n+            let mut used = USED_NAMES.lock().unwrap();\n+            used.remove(&old_name);\n+        }\n     }\n }\n+\n+/*\n+A brief note on API design:\n+\n+In this implementation, we were forced to use a global mutable state (USED_NAMES) so that every robot's name remains unique even when generated randomly.\n+However, a better design might avoid having a global state by introducing an explicit dependency injection.\n+For example, one could design a RobotFactory struct that maintains the set of used names and is responsible for constructing robots.\n+This way, the robot creation and name generation are tied to the factory instance, and the side-effect of maintaining uniqueness stays within that context.\n+Such an API would look like:\n+\n+    struct RobotFactory {\n+        used_names: HashSet<String>,\n+    }\n+\n+    impl RobotFactory {\n+        fn new_robot(&mut self) -> Robot { ... }\n+    }\n+\n+This approach makes the dependency explicit, and testing, resource management, and code comprehension become easier.\n+*/\n", "proposed_model_patches": [], "eval_result": "unresolved", "success": true}