{"instance_id": "rust__nucleotide-codons", "model_name_or_path": "20250510_021328--claude-3-5-sonnet-20241022_0", "model_patch": "diff --git a/src/lib.rs b/src/lib.rs\nindex 17a6829..0cd12e3 100644\n--- a/src/lib.rs\n+++ b/src/lib.rs\n@@ -1,14 +1,20 @@\n // This exercise is deprecated.\n // Consider working on protein-translation instead.\n \n-use std::marker::PhantomData;\n+// We implement a function that returns the name of an amino acid a particular codon,\n+// possibly using shorthand, encodes for. Given a collection of canonical (non-shorthand)\n+// codon to protein pairs, we allow a query codon that might include ambiguous nucleotides\n+// (such as R for A/G, etc). The query codon is valid if it matches one or more canonical\n+// codons and all matched canonical codons map to the same protein name. Otherwise, an error\n+// is returned.\n+\n+// Additionally, we implement RNA translation via the of_rna function. RNA strings are first\n+// converted (U -> T) and then segmented into codons of three. Translation stops if an encoding\n+// of STOP is encountered, and an error is returned if the RNA string's length is not a multiple\n+// of 3 or if any codon does not decode properly.\n \n pub struct CodonsInfo<'a> {\n-    // This field is here to make the template compile and not to\n-    // complain about unused type lifetime parameter \"'a\". Once you start\n-    // solving the exercise, delete this field and the 'std::marker::PhantomData'\n-    // import.\n-    phantom: PhantomData<&'a ()>,\n+    mapping: Vec<(&'a str, &'a str)>,\n }\n \n #[derive(Debug, Clone, Copy, PartialEq, Eq)]\n@@ -16,20 +22,82 @@ pub struct Error;\n \n impl<'a> CodonsInfo<'a> {\n     pub fn name_for(&self, codon: &str) -> Result<&'a str, Error> {\n-        todo!(\n-            \"Return the protein name for a '{}' codon or Err, if codon string is invalid\",\n-            codon\n-        );\n+        if codon.len() != 3 {\n+            return Err(Error);\n+        }\n+        let mut protein_result: Option<&'a str> = None;\n+        for &(canonical, protein) in &self.mapping {\n+            if matches_pattern(codon, canonical) {\n+                match protein_result {\n+                    None => protein_result = Some(protein),\n+                    Some(existing) if existing != protein => return Err(Error),\n+                    _ => {}\n+                }\n+            }\n+        }\n+        protein_result.ok_or(Error)\n     }\n \n     pub fn of_rna(&self, rna: &str) -> Result<Vec<&'a str>, Error> {\n-        todo!(\"Return a list of protein names that correspond to the '{}' RNA string or Err if the RNA string is invalid\", rna);\n+        if rna.len() % 3 != 0 {\n+            return Err(Error);\n+        }\n+        let mut proteins = Vec::new();\n+        for chunk in rna.as_bytes().chunks(3) {\n+            let codon_rna = std::str::from_utf8(chunk).map_err(|_| Error)?;\n+            // Convert RNA codon (which uses U) to DNA codon (which uses T).\n+            let codon = codon_rna.replace(\"U\", \"T\");\n+            let prot = self.name_for(&codon)?;\n+            if prot == \"STOP\" {\n+                break;\n+            }\n+            proteins.push(prot);\n+        }\n+        Ok(proteins)\n     }\n }\n \n pub fn parse<'a>(pairs: Vec<(&'a str, &'a str)>) -> CodonsInfo<'a> {\n-    todo!(\n-        \"Construct a new CodonsInfo struct from given pairs: {:?}\",\n-        pairs\n-    );\n+    CodonsInfo { mapping: pairs }\n+}\n+\n+// Helper function: returns the allowed bases for a given nucleotide or ambiguous nucleotide.\n+fn allowed_bases(ch: char) -> Option<&'static [char]> {\n+    match ch {\n+        'A' => Some(&['A']),\n+        'C' => Some(&['C']),\n+        'G' => Some(&['G']),\n+        'T' => Some(&['T']),\n+        'R' => Some(&['A', 'G']),\n+        'Y' => Some(&['C', 'T']),\n+        'S' => Some(&['G', 'C']),\n+        'W' => Some(&['A', 'T']),\n+        'K' => Some(&['G', 'T']),\n+        'M' => Some(&['A', 'C']),\n+        'B' => Some(&['C', 'G', 'T']),\n+        'D' => Some(&['A', 'G', 'T']),\n+        'H' => Some(&['A', 'C', 'T']),\n+        'V' => Some(&['A', 'C', 'G']),\n+        'N' => Some(&['A', 'C', 'G', 'T']),\n+        _ => None,\n+    }\n+}\n+\n+// Checks if the shorthand pattern codon matches the canonical codon.\n+// For each position, the canonical nucleotide must be one of the possibilities of the shorthand character.\n+fn matches_pattern(pattern: &str, canonical: &str) -> bool {\n+    if pattern.len() != 3 || canonical.len() != 3 {\n+        return false;\n+    }\n+    for (p, c) in pattern.chars().zip(canonical.chars()) {\n+        if let Some(bases) = allowed_bases(p) {\n+            if !bases.contains(&c) {\n+                return false;\n+            }\n+        } else {\n+            // Invalid nucleotide encountered in shorthand\n+            return false;\n+        }\n+    }\n+    true\n }\n", "proposed_model_patches": [], "eval_result": "resolved", "success": true}