{"instance_id": "python__grade-school", "model_name_or_path": "20250510_021328--claude-3-5-sonnet-20241022_0", "model_patch": "diff --git a/grade_school.py b/grade_school.py\nindex b57208f..49871cf 100644\n--- a/grade_school.py\n+++ b/grade_school.py\n@@ -1,15 +1,36 @@\n class School:\n     def __init__(self):\n-        pass\n+        # _roster maps grade (int) -> list of student names (sorted alphabetically)\n+        self._roster = {}\n+        # _students is a set of added student names to prevent duplicates\n+        self._students = set()\n \n     def add_student(self, name, grade):\n-        pass\n+        # Check for duplicate student name\n+        if name in self._students:\n+            raise ValueError(f\"Student '{name}' has already been added\")\n+\n+        self._students.add(name)\n+\n+        # Add student to the appropriate grade\n+        if grade not in self._roster:\n+            self._roster[grade] = []\n+        self._roster[grade].append(name)\n+        self._roster[grade].sort()\n+\n+        return \"OK.\"\n \n     def roster(self):\n-        pass\n+        # Collect students sorted by grade (1,2,3,...) and within each grade alphabetically\n+        result = []\n+        for grade in sorted(self._roster.keys()):\n+            result.extend(self._roster[grade])\n+        return result\n \n     def grade(self, grade_number):\n-        pass\n+        # Return a sorted list of students enrolled in the given grade\n+        return self._roster.get(grade_number, [])\n \n     def added(self):\n-        pass\n+        # Return a sorted list of all students that have been added (alphabetically)\n+        return sorted(list(self._students))\n", "proposed_model_patches": [], "eval_result": "unresolved", "success": true}