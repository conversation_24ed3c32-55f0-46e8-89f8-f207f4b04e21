{"instance_id": "rust__parallel-letter-frequency", "model_name_or_path": "20250510_021328--claude-3-5-sonnet-20241022_0", "model_patch": "diff --git a/src/lib.rs b/src/lib.rs\nindex 43b349c..a350080 100644\n--- a/src/lib.rs\n+++ b/src/lib.rs\n@@ -1,11 +1,58 @@\n use std::collections::HashMap;\n+use std::thread;\n+\n+/// Count the frequency of letters in the given input texts using `worker_count` parallel workers.\n+///\n+/// Only alphabetic characters are counted and they are normalized to lowercase.\n \n pub fn frequency(input: &[&str], worker_count: usize) -> HashMap<char, usize> {\n-    todo!(\n-        \"Count the frequency of letters in the given input '{input:?}'. Ensure that you are using {} to process the input.\",\n-        match worker_count {\n-            1 => \"1 worker\".to_string(),\n-            _ => format!(\"{worker_count} workers\"),\n+    // If worker_count is 1 or there's no input, do it sequentially\n+    if worker_count <= 1 || input.len() < 2 {\n+        let mut freq = HashMap::new();\n+        for &text in input {\n+            for c in text.chars() {\n+                if c.is_alphabetic() {\n+                    // normalize character to lowercase\n+                    let ch = c.to_ascii_lowercase();\n+                    *freq.entry(ch).or_insert(0) += 1;\n+                }\n+            }\n+        }\n+        return freq;\n+    }\n+\n+    // Determine chunk size: split the input slice into nearly equal chunks\n+    let total = input.len();\n+    let chunk_size = (total + worker_count - 1) / worker_count; // ceiling division\n+\n+    // Spawn threads to process chunks of the input\n+    let mut handles = Vec::new();\n+    for chunk in input.chunks(chunk_size) {\n+        // Clone the slice portion as a Vec, because the &str lifetimes still apply, but that's fine\n+        let texts: Vec<&str> = chunk.to_vec();\n+        let handle = thread::spawn(move || {\n+            let mut local_freq = HashMap::new();\n+            for text in texts {\n+                for c in text.chars() {\n+                    if c.is_alphabetic() {\n+                        let ch = c.to_ascii_lowercase();\n+                        *local_freq.entry(ch).or_insert(0) += 1;\n+                    }\n+                }\n+            }\n+            local_freq\n+        });\n+        handles.push(handle);\n+    }\n+\n+    // Merge all the partial frequency maps\n+    let mut result = HashMap::new();\n+    for handle in handles {\n+        let local_map = handle.join().expect(\"Thread panicked\");\n+        for (ch, count) in local_map {\n+            *result.entry(ch).or_insert(0) += count;\n         }\n-    );\n+    }\n+\n+    result\n }\n", "proposed_model_patches": [], "eval_result": "unresolved", "success": true}