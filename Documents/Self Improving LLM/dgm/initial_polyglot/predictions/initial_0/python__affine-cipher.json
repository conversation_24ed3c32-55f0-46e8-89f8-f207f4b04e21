{"instance_id": "python__affine-cipher", "model_name_or_path": "20250510_021328--claude-3-5-sonnet-20241022_0", "model_patch": "diff --git a/affine_cipher.py b/affine_cipher.py\nindex 2d41e04..4b06dea 100644\n--- a/affine_cipher.py\n+++ b/affine_cipher.py\n@@ -1,6 +1,84 @@\n+import math\n+\n+ALPHABET = 'abcdefghijklmnopqrstuvwxyz'\n+M = len(ALPHABET)\n+\n+\n+def mod_inverse(a, m):\n+    # Extended Euclidean Algorithm to find modular inverse of a modulo m\n+    # Returns the modular inverse of a mod m if it exists, else raises ValueError\n+    a = a % m\n+    if math.gcd(a, m) != 1:\n+        raise ValueError(f\"{a} and {m} are not coprime, so modular inverse does not exist.\")\n+    # Initialize variables for the Extended Euclidean Algorithm\n+    m0, x0, x1 = m, 0, 1\n+    while a > 1:\n+        # q is quotient\n+        q = a // m\n+        a, m = m, a % m\n+        x0, x1 = x1 - q * x0, x0\n+    # Make x1 positive\n+    if x1 < 0:\n+        x1 += m0\n+    return x1\n+\n+\n+def process_text(text):\n+    # Remove spaces and punctuation, but keep digits intact\n+    # Only letters and digits remain, letters are converted to lowercase\n+    result = []\n+    for char in text:\n+        if char.isalpha():\n+            result.append(char.lower())\n+        elif char.isdigit():\n+            result.append(char)\n+        # ignore spaces and punctuation\n+    return ''.join(result)\n+\n+\n+def group_text(text, group_size=5):\n+    # Group text into groups of group_size separated by spaces\n+    return ' '.join(text[i:i+group_size] for i in range(0, len(text), group_size))\n+\n+\n def encode(plain_text, a, b):\n-    pass\n+    # Check if a and M are coprime, else raise ValueError with a meaningful message\n+    if math.gcd(a, M) != 1:\n+        raise ValueError(f\"{a} and {M} are not coprime. Encryption cannot proceed.\")\n+\n+    processed = process_text(plain_text)\n+    cipher_chars = []\n+    for char in processed:\n+        if char.isalpha():\n+            index = ALPHABET.index(char)\n+            # encryption E(x) = (a*i + b) mod M\n+            new_index = (a * index + b) % M\n+            cipher_chars.append(ALPHABET[new_index])\n+        else:\n+            # for digits, do not encrypt\n+            cipher_chars.append(char)\n+    cipher_text = ''.join(cipher_chars)\n+    # Group the cipher text into blocks of 5\n+    return group_text(cipher_text, 5)\n \n \n def decode(ciphered_text, a, b):\n-    pass\n+    # Check if a and M are coprime, else raise ValueError with a meaningful message\n+    if math.gcd(a, M) != 1:\n+        raise ValueError(f\"{a} and {M} are not coprime. Decryption cannot proceed.\")\n+\n+    # Compute modular inverse of a mod M\n+    a_inv = mod_inverse(a, M)\n+\n+    # Remove spaces and punctuation from ciphered_text, preserve digits\n+    processed = process_text(ciphered_text)\n+    plain_chars = []\n+    for char in processed:\n+        if char.isalpha():\n+            index = ALPHABET.index(char)\n+            # decryption D(y) = a_inv * (y - b) mod M\n+            new_index = (a_inv * (index - b)) % M\n+            plain_chars.append(ALPHABET[new_index])\n+        else:\n+            plain_chars.append(char)\n+    return ''.join(plain_chars)\n", "proposed_model_patches": [], "eval_result": "unresolved", "success": true}