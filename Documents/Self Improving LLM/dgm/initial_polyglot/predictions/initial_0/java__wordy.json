{"instance_id": "java__wordy", "model_name_or_path": "20250510_021328--claude-3-5-sonnet-20241022_0", "model_patch": "diff --git a/src/main/java/WordProblemSolver.java b/src/main/java/WordProblemSolver.java\nindex 4b584c9..fa2ca96 100644\n--- a/src/main/java/WordProblemSolver.java\n+++ b/src/main/java/WordProblemSolver.java\n@@ -1,5 +1,97 @@\n class WordProblemSolver {\n     int solve(final String wordProblem) {\n-        throw new UnsupportedOperationException(\"Please implement the WordProblemSolver.solve() method.\");\n+        if (wordProblem == null || !wordProblem.startsWith(\"What is\") || !wordProblem.endsWith(\"?\")) {\n+            throw new IllegalArgumentException(\"Invalid question format\");\n+        }\n+\n+        // Remove the prefix \"What is \" and the trailing \"?\"\n+        String problem = wordProblem.substring(8, wordProblem.length() - 1).trim();\n+        if(problem.isEmpty()) {\n+            throw new IllegalArgumentException(\"No problem to solve\");\n+        }\n+\n+        String[] tokens = problem.split(\" \");\n+        // We support operations: plus, minus, multiplied by, divided by\n+        // We'll iterate token by token processing left-to-right\n+\n+        // The first token(s) might represent a number, handling negative numbers if present.\n+        // It might be like \"-3\" or \"5\".\n+\n+        int index = 0;\n+        int result;\n+        try {\n+            result = Integer.parseInt(tokens[index]);\n+        } catch (NumberFormatException e) {\n+            throw new IllegalArgumentException(\"Invalid number: \" + tokens[index]);\n+        }\n+        index++;\n+\n+        while (index < tokens.length) {\n+            String op = tokens[index];\n+            index++;\n+            int operand;\n+            if (op.equals(\"plus\")) {\n+                if (index >= tokens.length) {\n+                    throw new IllegalArgumentException(\"Expected a number after plus\");\n+                }\n+                try {\n+                    operand = Integer.parseInt(tokens[index]);\n+                } catch (NumberFormatException e) {\n+                    throw new IllegalArgumentException(\"Invalid number: \" + tokens[index]);\n+                }\n+                result += operand;\n+                index++;\n+            } else if (op.equals(\"minus\")) {\n+                if (index >= tokens.length) {\n+                    throw new IllegalArgumentException(\"Expected a number after minus\");\n+                }\n+                try {\n+                    operand = Integer.parseInt(tokens[index]);\n+                } catch (NumberFormatException e) {\n+                    throw new IllegalArgumentException(\"Invalid number: \" + tokens[index]);\n+                }\n+                result -= operand;\n+                index++;\n+            } else if (op.equals(\"multiplied\")) {\n+                // next token must be \"by\"\n+                if (index >= tokens.length || !tokens[index].equals(\"by\")) {\n+                    throw new IllegalArgumentException(\"Expected 'by' after multiplied\");\n+                }\n+                index++;\n+                if (index >= tokens.length) {\n+                    throw new IllegalArgumentException(\"Expected a number after multiplied by\");\n+                }\n+                try {\n+                    operand = Integer.parseInt(tokens[index]);\n+                } catch (NumberFormatException e) {\n+                    throw new IllegalArgumentException(\"Invalid number: \" + tokens[index]);\n+                }\n+                result *= operand;\n+                index++;\n+            } else if (op.equals(\"divided\")) {\n+                // next token must be \"by\"\n+                if (index >= tokens.length || !tokens[index].equals(\"by\")) {\n+                    throw new IllegalArgumentException(\"Expected 'by' after divided\");\n+                }\n+                index++;\n+                if (index >= tokens.length) {\n+                    throw new IllegalArgumentException(\"Expected a number after divided by\");\n+                }\n+                try {\n+                    operand = Integer.parseInt(tokens[index]);\n+                } catch (NumberFormatException e) {\n+                    throw new IllegalArgumentException(\"Invalid number: \" + tokens[index]);\n+                }\n+                if(operand == 0) {\n+                    throw new IllegalArgumentException(\"Division by zero\");\n+                }\n+                result /= operand;\n+                index++;\n+            } else {\n+                throw new IllegalArgumentException(\"Unsupported operation: \" + op);\n+            }\n+        }\n+\n+        return result;\n     }\n }\n", "proposed_model_patches": [], "eval_result": "unresolved", "success": true}