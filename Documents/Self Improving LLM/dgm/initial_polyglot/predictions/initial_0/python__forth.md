
========== User Instruction ==========
I have uploaded a code repository in the directory /testbed/. Help solve the following problem.

<problem_description>
# Instructions append

## Customizing and Raising Exceptions

Sometimes it is necessary to both [customize](https://docs.python.org/3/tutorial/errors.html#user-defined-exceptions) and [`raise`](https://docs.python.org/3/tutorial/errors.html#raising-exceptions) exceptions in your code. When you do this, you should always include a **meaningful error message** to indicate what the source of the error is. This makes your code more readable and helps significantly with debugging. 

Custom exceptions can be created through new exception classes (see [`classes`](https://docs.python.org/3/tutorial/classes.html#tut-classes) for more detail.) that are typically subclasses of [`Exception`](https://docs.python.org/3/library/exceptions.html#Exception).

For situations where you know the error source will be a derivative of a certain exception type, you can choose to inherit from one of the [`built in error types`](https://docs.python.org/3/library/exceptions.html#base-classes) under the _Exception_ class. When raising the error, you should still include a meaningful message.

This particular exercise requires that you create a _custom exception_ to be [raised](https://docs.python.org/3/reference/simple_stmts.html#the-raise-statement)/"thrown" when the stack is not sufficiently filled. The tests will only pass if you customize an appropriate exception, `raise` that exception, and include appropriate error messages.


```python
# subclassing the Exception to create a StackUnderflowError
class StackUnderflowError(Exception):
    """Exception raised when Stack is not full.
       message: explanation of the error.
    """
    def __init__(self, message):
        self.message = message

        
# raising a StackUnderflowError
raise StackUnderflowError("Insufficient number of items in stack")
```

Additionally, this exercise requires that you raise several `built-in exceptions` with error messages.
To raise a `built-in exception` with a message, write the message as an argument to the `exception` type:

```python
# an example when division by zero is attempted.
raise ZeroDivisionError("divide by zero")

#an example when the operation is undefined.
raise ValueError("undefined operation")
```


# Instructions

Implement an evaluator for a very simple subset of Forth.

[Forth][forth]
is a stack-based programming language.
Implement a very basic evaluator for a small subset of Forth.

Your evaluator has to support the following words:

- `+`, `-`, `*`, `/` (integer arithmetic)
- `DUP`, `DROP`, `SWAP`, `OVER` (stack manipulation)

Your evaluator also has to support defining new words using the customary syntax: `: word-name definition ;`.

To keep things simple the only data type you need to support is signed integers of at least 16 bits size.

You should use the following rules for the syntax: a number is a sequence of one or more (ASCII) digits, a word is a sequence of one or more letters, digits, symbols or punctuation that is not a number.
(Forth probably uses slightly different rules, but this is close enough.)

Words are case-insensitive.

[forth]: https://en.wikipedia.org/wiki/Forth_%28programming_language%29



</problem_description>

Your task is to make changes to the files in the /testbed/ directory to address the <problem_description>. I have already taken care of the required dependencies.


Please analyze the problem description carefully. Then make edits to the code files to complete the instruction.

========== Agent Response ==========
Response(id='resp_681ebd1bdf6c8191bfabaccf09dd262a0be412d814e6b7b3', created_at=1746844955.0, error=None, incomplete_details=None, instructions=None, metadata={}, model='o3-mini-2025-01-31', object='response', output=[ResponseReasoningItem(id='rs_681ebd1cbd6881918fc206a4077019e30be412d814e6b7b3', summary=[], type='reasoning', encrypted_content=None, status=None), ResponseFunctionToolCall(arguments='{"command": "view", "path": "/testbed/README.md", "file_text": null}', call_id='call_99WiJYYMyPDvHYiPcuMmf9D5', name='editor', type='function_call', id='fc_681ebd1d85f48191ab2bf2c02ba77d790be412d814e6b7b3', status='completed')], parallel_tool_calls=False, temperature=1.0, tool_choice='auto', tools=[FunctionTool(name='bash', parameters={'type': 'object', 'properties': {'command': {'type': 'string', 'description': 'The bash command to run.'}}, 'required': ['command'], 'additionalProperties': False}, strict=True, type='function', description='Run commands in a bash shell\n\n* When invoking this tool, the contents of the "command" parameter does NOT need to be XML-escaped.\n\n* You don\'t have access to the internet via this tool.\n\n* You do have access to a mirror of common linux and python packages via apt and pip.\n\n* State is persistent across command calls and discussions with the user.\n\n* To inspect a particular line range of a file, e.g. lines 10-25, try \'sed -n 10,25p /path/to/the/file\'.\n\n* Please avoid commands that may produce a very large amount of output.\n\n* Please run long lived commands in the background, e.g. \'sleep 10 &\' or start a server in the background.'), FunctionTool(name='editor', parameters={'type': 'object', 'properties': {'command': {'type': 'string', 'enum': ['view', 'create', 'edit'], 'description': 'The command to run: `view`, `create`, or `edit`.'}, 'path': {'description': 'Absolute path to file or directory, e.g. `/repo/file.py` or `/repo`.', 'type': 'string'}, 'file_text': {'description': 'Required parameter of `create` or `edit` command, containing the content for the entire file.', 'type': ['string', 'null']}}, 'required': ['command', 'path', 'file_text'], 'additionalProperties': False}, strict=True, type='function', description='Custom editing tool for viewing, creating, and editing files\n\n* State is persistent across command calls and discussions with the user.\n\n* If `path` is a file, `view` displays the entire file with line numbers. If `path` is a directory, `view` lists non-hidden files and directories up to 2 levels deep.\n\n* The `create` command cannot be used if the specified `path` already exists as a file.\n\n* If a `command` generates a long output, it will be truncated and marked with `<response clipped>`.\n\n* The `edit` command overwrites the entire file with the provided `file_text`.\n\n* No partial/line-range edits or partial viewing are supported.')], top_p=1.0, max_output_tokens=None, previous_response_id=None, reasoning=Reasoning(effort='medium', generate_summary=None, summary=None), service_tier='default', status='completed', text=ResponseTextConfig(format=ResponseFormatText(type='text')), truncation='disabled', usage=ResponseUsage(input_tokens=1224, input_tokens_details=InputTokensDetails(cached_tokens=0), output_tokens=92, output_tokens_details=OutputTokensDetails(reasoning_tokens=64), total_tokens=1316), user=None, store=True)
{'tool_id': 'call_99WiJYYMyPDvHYiPcuMmf9D5', 'tool_name': 'editor', 'tool_input': {'command': 'view', 'path': '/testbed/README.md', 'file_text': None}}
Tool Used: editor
Tool Input: {'command': 'view', 'path': '/testbed/README.md', 'file_text': None}
Tool Result: Error: The path /testbed/README.md does not exist.
Tool Response: Response(id='resp_681ebd1e731c8191b75db410895ef1250be412d814e6b7b3', created_at=1746844958.0, error=None, incomplete_details=None, instructions=None, metadata={}, model='o3-mini-2025-01-31', object='response', output=[ResponseReasoningItem(id='rs_681ebd1f73a081919186ea10923ac7080be412d814e6b7b3', summary=[], type='reasoning', encrypted_content=None, status=None), ResponseFunctionToolCall(arguments='{"command": "view", "path": "/testbed", "file_text": null}', call_id='call_BnrSR4Vrvh4F8wYwbJgRUTeM', name='editor', type='function_call', id='fc_681ebd1fb5bc819194e1009a028864650be412d814e6b7b3', status='completed')], parallel_tool_calls=False, temperature=1.0, tool_choice='auto', tools=[FunctionTool(name='bash', parameters={'type': 'object', 'properties': {'command': {'type': 'string', 'description': 'The bash command to run.'}}, 'required': ['command'], 'additionalProperties': False}, strict=True, type='function', description='Run commands in a bash shell\n\n* When invoking this tool, the contents of the "command" parameter does NOT need to be XML-escaped.\n\n* You don\'t have access to the internet via this tool.\n\n* You do have access to a mirror of common linux and python packages via apt and pip.\n\n* State is persistent across command calls and discussions with the user.\n\n* To inspect a particular line range of a file, e.g. lines 10-25, try \'sed -n 10,25p /path/to/the/file\'.\n\n* Please avoid commands that may produce a very large amount of output.\n\n* Please run long lived commands in the background, e.g. \'sleep 10 &\' or start a server in the background.'), FunctionTool(name='editor', parameters={'type': 'object', 'properties': {'command': {'type': 'string', 'enum': ['view', 'create', 'edit'], 'description': 'The command to run: `view`, `create`, or `edit`.'}, 'path': {'description': 'Absolute path to file or directory, e.g. `/repo/file.py` or `/repo`.', 'type': 'string'}, 'file_text': {'description': 'Required parameter of `create` or `edit` command, containing the content for the entire file.', 'type': ['string', 'null']}}, 'required': ['command', 'path', 'file_text'], 'additionalProperties': False}, strict=True, type='function', description='Custom editing tool for viewing, creating, and editing files\n\n* State is persistent across command calls and discussions with the user.\n\n* If `path` is a file, `view` displays the entire file with line numbers. If `path` is a directory, `view` lists non-hidden files and directories up to 2 levels deep.\n\n* The `create` command cannot be used if the specified `path` already exists as a file.\n\n* If a `command` generates a long output, it will be truncated and marked with `<response clipped>`.\n\n* The `edit` command overwrites the entire file with the provided `file_text`.\n\n* No partial/line-range edits or partial viewing are supported.')], top_p=1.0, max_output_tokens=None, previous_response_id=None, reasoning=Reasoning(effort='medium', generate_summary=None, summary=None), service_tier='default', status='completed', text=ResponseTextConfig(format=ResponseFormatText(type='text')), truncation='disabled', usage=ResponseUsage(input_tokens=1273, input_tokens_details=InputTokensDetails(cached_tokens=1214), output_tokens=89, output_tokens_details=OutputTokensDetails(reasoning_tokens=64), total_tokens=1362), user=None, store=True)
Tool Used: editor
Tool Input: {'command': 'view', 'path': '/testbed', 'file_text': None}
Tool Result: Here's the files and directories up to 2 levels deep in /testbed, excluding hidden items:
/testbed
/testbed/forth.py

Tool Response: Response(id='resp_681ebd207078819183092304619549280be412d814e6b7b3', created_at=1746844960.0, error=None, incomplete_details=None, instructions=None, metadata={}, model='o3-mini-2025-01-31', object='response', output=[ResponseReasoningItem(id='rs_681ebd2153dc81918b17dcdf792ea1350be412d814e6b7b3', summary=[], type='reasoning', encrypted_content=None, status=None), ResponseFunctionToolCall(arguments='{"command": "view", "path": "/testbed/forth.py", "file_text": null}', call_id='call_LlWQherSBXIqtSgetsmord03', name='editor', type='function_call', id='fc_681ebd22140481918104d1a33d57fc9e0be412d814e6b7b3', status='completed')], parallel_tool_calls=False, temperature=1.0, tool_choice='auto', tools=[FunctionTool(name='bash', parameters={'type': 'object', 'properties': {'command': {'type': 'string', 'description': 'The bash command to run.'}}, 'required': ['command'], 'additionalProperties': False}, strict=True, type='function', description='Run commands in a bash shell\n\n* When invoking this tool, the contents of the "command" parameter does NOT need to be XML-escaped.\n\n* You don\'t have access to the internet via this tool.\n\n* You do have access to a mirror of common linux and python packages via apt and pip.\n\n* State is persistent across command calls and discussions with the user.\n\n* To inspect a particular line range of a file, e.g. lines 10-25, try \'sed -n 10,25p /path/to/the/file\'.\n\n* Please avoid commands that may produce a very large amount of output.\n\n* Please run long lived commands in the background, e.g. \'sleep 10 &\' or start a server in the background.'), FunctionTool(name='editor', parameters={'type': 'object', 'properties': {'command': {'type': 'string', 'enum': ['view', 'create', 'edit'], 'description': 'The command to run: `view`, `create`, or `edit`.'}, 'path': {'description': 'Absolute path to file or directory, e.g. `/repo/file.py` or `/repo`.', 'type': 'string'}, 'file_text': {'description': 'Required parameter of `create` or `edit` command, containing the content for the entire file.', 'type': ['string', 'null']}}, 'required': ['command', 'path', 'file_text'], 'additionalProperties': False}, strict=True, type='function', description='Custom editing tool for viewing, creating, and editing files\n\n* State is persistent across command calls and discussions with the user.\n\n* If `path` is a file, `view` displays the entire file with line numbers. If `path` is a directory, `view` lists non-hidden files and directories up to 2 levels deep.\n\n* The `create` command cannot be used if the specified `path` already exists as a file.\n\n* If a `command` generates a long output, it will be truncated and marked with `<response clipped>`.\n\n* The `edit` command overwrites the entire file with the provided `file_text`.\n\n* No partial/line-range edits or partial viewing are supported.')], top_p=1.0, max_output_tokens=None, previous_response_id=None, reasoning=Reasoning(effort='medium', generate_summary=None, summary=None), service_tier='default', status='completed', text=ResponseTextConfig(format=ResponseFormatText(type='text')), truncation='disabled', usage=ResponseUsage(input_tokens=1334, input_tokens_details=InputTokensDetails(cached_tokens=0), output_tokens=28, output_tokens_details=OutputTokensDetails(reasoning_tokens=0), total_tokens=1362), user=None, store=True)
Tool Used: editor
Tool Input: {'command': 'view', 'path': '/testbed/forth.py', 'file_text': None}
Tool Result: Here's the result of running `cat -n` on /testbed/forth.py:
```
     1	class StackUnderflowError(Exception):
     2	    pass
     3	
     4	
     5	def evaluate(input_data):
     6	    pass
     7	
```

Tool Response: Response(id='resp_681ebd22f1b08191986bfc740b6a8a800be412d814e6b7b3', created_at=1746844963.0, error=None, incomplete_details=None, instructions=None, metadata={}, model='o3-mini-2025-01-31', object='response', output=[ResponseReasoningItem(id='rs_681ebd2d2f2c8191a757365b3e0b2dd50be412d814e6b7b3', summary=[], type='reasoning', encrypted_content=None, status=None), ResponseFunctionToolCall(arguments='{"command": "edit", "path": "/testbed/forth.py", "file_text": "class StackUnderflowError(Exception):\\n    \\"\\"\\"Exception raised when the stack does not contain enough values.\\"\\"\\"\\n    def __init__(self, message):\\n        self.message = message\\n        super().__init__(message)\\n\\n\\n\\ndef evaluate(input_data):\\n    \\"\\"\\"\\n    Evaluate a string of Forth code and return the stack as a list of integers.\\n    Supports integer arithmetic, stack manipulation, and custom word definitions.\\n    \\n    The following built-in operations are supported:\\n        +, -, *, /\\n        DUP, DROP, SWAP, OVER\\n    New words can be defined via the syntax:\\n        : word-name definition ;\\n    Words are case-insensitive.\\n    \\"\\"\\"\\n    # Split the input into tokens. We use simple whitespace splitting.\\n    # We also convert tokens to lower-case for uniformity (custom words and built-in operations are case-insensitive).\\n    tokens = input_data.split()\\n    # We\'ll preserve the original token for numbers (if needed) but all commands are treated in lower-case\\n    tokens = [token for token in tokens]\\n\\n    # dictionary to hold custom word definitions (mapping lower-case word to list of tokens)\\n    definitions = {}\\n\\n    # We first perform a pre-processing pass to handle custom word definitions.\\n    # In Forth, definitions can appear anywhere in the input and are removed from the main token stream.\\n    processed_tokens = []\\n    i = 0\\n    while i < len(tokens):\\n        token = tokens[i]\\n        if token == \':\' :\\n            # Start of a definition\\n            # Next token is the new word name\\n            i += 1\\n            if i >= len(tokens):\\n                raise ValueError(\'Invalid definition: word name missing\')\\n            word_name = tokens[i].lower()\\n            i += 1\\n            # collect definition tokens until we find a \';\'\\n            definition_tokens = []\\n            while i < len(tokens) and tokens[i] != \';\':\\n                # store as lower-case if not a number literal (but we need to preserve the literal? Actually numbers can be processed either way.)\\n                # However, since our language is case-insensitive and numbers are treated as numbers regardless,\\n                # it\'s safe to lower-case everything that is not numeric. But to be safe, we just lower-case all tokens.\\n                definition_tokens.append(tokens[i])\\n                i += 1\\n            if i >= len(tokens) or tokens[i] != \';\':\\n                raise ValueError(\'Invalid definition: missing \\";\\"\')\\n            # store definition tokens in dictionary\\n            definitions[word_name] = [t for t in definition_tokens]\\n            i += 1  # Skip the \';\'\\n        else:\\n            processed_tokens.append(token)\\n            i += 1\\n\\n    # Now, process the main token stream with definitions expanded.\\n    # We\'ll use a list as our token queue. We\'ll insert definitions tokens at the beginning when a custom word is encountered.\\n    # Convert tokens to lower-case for non-number tokens during evaluation.\\n    token_queue = processed_tokens\\n    stack = []\\n\\n    # Define built-in operations in a dictionary mapping token to a function\\n    def add():\\n        if len(stack) < 2:\\n            raise StackUnderflowError(\\"Insufficient number of items in stack for \'+\' operation\\")\\n        b = stack.pop()\\n        a = stack.pop()\\n        stack.append(a + b)\\n\\n    def subtract():\\n        if len(stack) < 2:\\n            raise StackUnderflowError(\\"Insufficient number of items in stack for \'-\' operation\\")\\n        b = stack.pop()\\n        a = stack.pop()\\n        stack.append(a - b)\\n\\n    def multiply():\\n        if len(stack) < 2:\\n            raise StackUnderflowError(\\"Insufficient number of items in stack for \'*\' operation\\")\\n        b = stack.pop()\\n        a = stack.pop()\\n        stack.append(a * b)\\n\\n    def divide():\\n        if len(stack) < 2:\\n            raise StackUnderflowError(\\"Insufficient number of items in stack for \'/\' operation\\")\\n        b = stack.pop()\\n        a = stack.pop()\\n        if b == 0:\\n            raise ZeroDivisionError(\\"divide by zero\\")\\n        # Forth typically uses integer division that truncates toward zero\\n        # Python\'s int division using // floors towards negative infinity, so we mimic truncation towards zero\\n        # We can do: int(a / b) since a and b are ints\\n        result = int(a / b)\\n        stack.append(result)\\n\\n    def dup():\\n        if len(stack) < 1:\\n            raise StackUnderflowError(\\"Insufficient number of items in stack for DUP\\")\\n        stack.append(stack[-1])\\n\\n    def drop():\\n        if len(stack) < 1:\\n            raise StackUnderflowError(\\"Insufficient number of items in stack for DROP\\")\\n        stack.pop()\\n\\n    def swap():\\n        if len(stack) < 2:\\n            raise StackUnderflowError(\\"Insufficient number of items in stack for SWAP\\")\\n        stack[-1], stack[-2] = stack[-2], stack[-1]\\n\\n    def over():\\n        if len(stack) < 2:\\n            raise StackUnderflowError(\\"Insufficient number of items in stack for OVER\\")\\n        stack.append(stack[-2])\\n\\n    builtins = {\\n        \'+\': add,\\n        \'-\': subtract,\\n        \'*\': multiply,\\n        \'/\': divide,\\n        \'dup\': dup,\\n        \'drop\': drop,\\n        \'swap\': swap,\\n        \'over\': over\\n    }\\n\\n    # Process tokens:\\n    # We\'ll iterate as long as there are tokens in token_queue.\\n    # For each token, if it\'s a number, push to stack. If it\'s an operator, perform the operation.\\n    # If it\'s a custom word, expand it.\\n    while token_queue:\\n        token = token_queue.pop(0)\\n        # Try to interpret the token as an integer.\\n        # A token is a number if it represents a valid integer literal (we use int conversion).\\n        try:\\n            # If the token starts with \'-\' or digit, try integer parse.\\n            # But note: a token like \'-3\' is valid.\\n            value = int(token)\\n            stack.append(value)\\n            continue\\n        except ValueError:\\n            pass\\n\\n        token_lower = token.lower()\\n        if token_lower in builtins:\\n            # execute the built-in operation\\n            builtins[token_lower]()\\n        elif token_lower in definitions:\\n            # expand custom definition: insert its tokens at the front of the token queue.\\n            # We preserve order: the first token in the definition should be executed first.\\n            # Create a copy of the definition tokens.\\n            definition_tokens = definitions[token_lower][:]\\n            # We need to insert these tokens to the beginning of the queue\\n            token_queue = definition_tokens + token_queue\\n        else:\\n            # if token is not recognized, it\'s an undefined operation.\\n            # Raise a ValueError with a meaningful message.\\n            raise ValueError(f\\"undefined operation: {token}\\")\\n\\n    return stack\\n"}', call_id='call_UjTCmOpapIHmlcrVAUFxqIpb', name='editor', type='function_call', id='fc_681ebd2f31e081918502bb8436ad51010be412d814e6b7b3', status='completed')], parallel_tool_calls=False, temperature=1.0, tool_choice='auto', tools=[FunctionTool(name='bash', parameters={'type': 'object', 'properties': {'command': {'type': 'string', 'description': 'The bash command to run.'}}, 'required': ['command'], 'additionalProperties': False}, strict=True, type='function', description='Run commands in a bash shell\n\n* When invoking this tool, the contents of the "command" parameter does NOT need to be XML-escaped.\n\n* You don\'t have access to the internet via this tool.\n\n* You do have access to a mirror of common linux and python packages via apt and pip.\n\n* State is persistent across command calls and discussions with the user.\n\n* To inspect a particular line range of a file, e.g. lines 10-25, try \'sed -n 10,25p /path/to/the/file\'.\n\n* Please avoid commands that may produce a very large amount of output.\n\n* Please run long lived commands in the background, e.g. \'sleep 10 &\' or start a server in the background.'), FunctionTool(name='editor', parameters={'type': 'object', 'properties': {'command': {'type': 'string', 'enum': ['view', 'create', 'edit'], 'description': 'The command to run: `view`, `create`, or `edit`.'}, 'path': {'description': 'Absolute path to file or directory, e.g. `/repo/file.py` or `/repo`.', 'type': 'string'}, 'file_text': {'description': 'Required parameter of `create` or `edit` command, containing the content for the entire file.', 'type': ['string', 'null']}}, 'required': ['command', 'path', 'file_text'], 'additionalProperties': False}, strict=True, type='function', description='Custom editing tool for viewing, creating, and editing files\n\n* State is persistent across command calls and discussions with the user.\n\n* If `path` is a file, `view` displays the entire file with line numbers. If `path` is a directory, `view` lists non-hidden files and directories up to 2 levels deep.\n\n* The `create` command cannot be used if the specified `path` already exists as a file.\n\n* If a `command` generates a long output, it will be truncated and marked with `<response clipped>`.\n\n* The `edit` command overwrites the entire file with the provided `file_text`.\n\n* No partial/line-range edits or partial viewing are supported.')], top_p=1.0, max_output_tokens=None, previous_response_id=None, reasoning=Reasoning(effort='medium', generate_summary=None, summary=None), service_tier='default', status='completed', text=ResponseTextConfig(format=ResponseFormatText(type='text')), truncation='disabled', usage=ResponseUsage(input_tokens=1433, input_tokens_details=InputTokensDetails(cached_tokens=0), output_tokens=2914, output_tokens_details=OutputTokensDetails(reasoning_tokens=1280), total_tokens=4347), user=None, store=True)
Tool Used: editor
Tool Input: {'command': 'edit', 'path': '/testbed/forth.py', 'file_text': 'class StackUnderflowError(Exception):\n    """Exception raised when the stack does not contain enough values."""\n    def __init__(self, message):\n        self.message = message\n        super().__init__(message)\n\n\n\ndef evaluate(input_data):\n    """\n    Evaluate a string of Forth code and return the stack as a list of integers.\n    Supports integer arithmetic, stack manipulation, and custom word definitions.\n    \n    The following built-in operations are supported:\n        +, -, *, /\n        DUP, DROP, SWAP, OVER\n    New words can be defined via the syntax:\n        : word-name definition ;\n    Words are case-insensitive.\n    """\n    # Split the input into tokens. We use simple whitespace splitting.\n    # We also convert tokens to lower-case for uniformity (custom words and built-in operations are case-insensitive).\n    tokens = input_data.split()\n    # We\'ll preserve the original token for numbers (if needed) but all commands are treated in lower-case\n    tokens = [token for token in tokens]\n\n    # dictionary to hold custom word definitions (mapping lower-case word to list of tokens)\n    definitions = {}\n\n    # We first perform a pre-processing pass to handle custom word definitions.\n    # In Forth, definitions can appear anywhere in the input and are removed from the main token stream.\n    processed_tokens = []\n    i = 0\n    while i < len(tokens):\n        token = tokens[i]\n        if token == \':\' :\n            # Start of a definition\n            # Next token is the new word name\n            i += 1\n            if i >= len(tokens):\n                raise ValueError(\'Invalid definition: word name missing\')\n            word_name = tokens[i].lower()\n            i += 1\n            # collect definition tokens until we find a \';\'\n            definition_tokens = []\n            while i < len(tokens) and tokens[i] != \';\':\n                # store as lower-case if not a number literal (but we need to preserve the literal? Actually numbers can be processed either way.)\n                # However, since our language is case-insensitive and numbers are treated as numbers regardless,\n                # it\'s safe to lower-case everything that is not numeric. But to be safe, we just lower-case all tokens.\n                definition_tokens.append(tokens[i])\n                i += 1\n            if i >= len(tokens) or tokens[i] != \';\':\n                raise ValueError(\'Invalid definition: missing ";"\')\n            # store definition tokens in dictionary\n            definitions[word_name] = [t for t in definition_tokens]\n            i += 1  # Skip the \';\'\n        else:\n            processed_tokens.append(token)\n            i += 1\n\n    # Now, process the main token stream with definitions expanded.\n    # We\'ll use a list as our token queue. We\'ll insert definitions tokens at the beginning when a custom word is encountered.\n    # Convert tokens to lower-case for non-number tokens during evaluation.\n    token_queue = processed_tokens\n    stack = []\n\n    # Define built-in operations in a dictionary mapping token to a function\n    def add():\n        if len(stack) < 2:\n            raise StackUnderflowError("Insufficient number of items in stack for \'+\' operation")\n        b = stack.pop()\n        a = stack.pop()\n        stack.append(a + b)\n\n    def subtract():\n        if len(stack) < 2:\n            raise StackUnderflowError("Insufficient number of items in stack for \'-\' operation")\n        b = stack.pop()\n        a = stack.pop()\n        stack.append(a - b)\n\n    def multiply():\n        if len(stack) < 2:\n            raise StackUnderflowError("Insufficient number of items in stack for \'*\' operation")\n        b = stack.pop()\n        a = stack.pop()\n        stack.append(a * b)\n\n    def divide():\n        if len(stack) < 2:\n            raise StackUnderflowError("Insufficient number of items in stack for \'/\' operation")\n        b = stack.pop()\n        a = stack.pop()\n        if b == 0:\n            raise ZeroDivisionError("divide by zero")\n        # Forth typically uses integer division that truncates toward zero\n        # Python\'s int division using // floors towards negative infinity, so we mimic truncation towards zero\n        # We can do: int(a / b) since a and b are ints\n        result = int(a / b)\n        stack.append(result)\n\n    def dup():\n        if len(stack) < 1:\n            raise StackUnderflowError("Insufficient number of items in stack for DUP")\n        stack.append(stack[-1])\n\n    def drop():\n        if len(stack) < 1:\n            raise StackUnderflowError("Insufficient number of items in stack for DROP")\n        stack.pop()\n\n    def swap():\n        if len(stack) < 2:\n            raise StackUnderflowError("Insufficient number of items in stack for SWAP")\n        stack[-1], stack[-2] = stack[-2], stack[-1]\n\n    def over():\n        if len(stack) < 2:\n            raise StackUnderflowError("Insufficient number of items in stack for OVER")\n        stack.append(stack[-2])\n\n    builtins = {\n        \'+\': add,\n        \'-\': subtract,\n        \'*\': multiply,\n        \'/\': divide,\n        \'dup\': dup,\n        \'drop\': drop,\n        \'swap\': swap,\n        \'over\': over\n    }\n\n    # Process tokens:\n    # We\'ll iterate as long as there are tokens in token_queue.\n    # For each token, if it\'s a number, push to stack. If it\'s an operator, perform the operation.\n    # If it\'s a custom word, expand it.\n    while token_queue:\n        token = token_queue.pop(0)\n        # Try to interpret the token as an integer.\n        # A token is a number if it represents a valid integer literal (we use int conversion).\n        try:\n            # If the token starts with \'-\' or digit, try integer parse.\n            # But note: a token like \'-3\' is valid.\n            value = int(token)\n            stack.append(value)\n            continue\n        except ValueError:\n            pass\n\n        token_lower = token.lower()\n        if token_lower in builtins:\n            # execute the built-in operation\n            builtins[token_lower]()\n        elif token_lower in definitions:\n            # expand custom definition: insert its tokens at the front of the token queue.\n            # We preserve order: the first token in the definition should be executed first.\n            # Create a copy of the definition tokens.\n            definition_tokens = definitions[token_lower][:]\n            # We need to insert these tokens to the beginning of the queue\n            token_queue = definition_tokens + token_queue\n        else:\n            # if token is not recognized, it\'s an undefined operation.\n            # Raise a ValueError with a meaningful message.\n            raise ValueError(f"undefined operation: {token}")\n\n    return stack\n'}
Tool Result: File at /testbed/forth.py has been overwritten with new content.
