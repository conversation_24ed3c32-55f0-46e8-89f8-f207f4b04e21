{"instance_id": "python__forth", "model_name_or_path": "20250510_021328--claude-3-5-sonnet-20241022_0", "model_patch": "diff --git a/forth.py b/forth.py\nindex 7cf4ff3..4448d9b 100644\n--- a/forth.py\n+++ b/forth.py\n@@ -1,6 +1,166 @@\n class StackUnderflowError(Exception):\n-    pass\n+    \"\"\"Exception raised when the stack does not contain enough values.\"\"\"\n+    def __init__(self, message):\n+        self.message = message\n+        super().__init__(message)\n+\n \n \n def evaluate(input_data):\n-    pass\n+    \"\"\"\n+    Evaluate a string of Forth code and return the stack as a list of integers.\n+    Supports integer arithmetic, stack manipulation, and custom word definitions.\n+    \n+    The following built-in operations are supported:\n+        +, -, *, /\n+        DUP, DROP, SWAP, OVER\n+    New words can be defined via the syntax:\n+        : word-name definition ;\n+    Words are case-insensitive.\n+    \"\"\"\n+    # Split the input into tokens. We use simple whitespace splitting.\n+    # We also convert tokens to lower-case for uniformity (custom words and built-in operations are case-insensitive).\n+    tokens = input_data.split()\n+    # We'll preserve the original token for numbers (if needed) but all commands are treated in lower-case\n+    tokens = [token for token in tokens]\n+\n+    # dictionary to hold custom word definitions (mapping lower-case word to list of tokens)\n+    definitions = {}\n+\n+    # We first perform a pre-processing pass to handle custom word definitions.\n+    # In Forth, definitions can appear anywhere in the input and are removed from the main token stream.\n+    processed_tokens = []\n+    i = 0\n+    while i < len(tokens):\n+        token = tokens[i]\n+        if token == ':' :\n+            # Start of a definition\n+            # Next token is the new word name\n+            i += 1\n+            if i >= len(tokens):\n+                raise ValueError('Invalid definition: word name missing')\n+            word_name = tokens[i].lower()\n+            i += 1\n+            # collect definition tokens until we find a ';'\n+            definition_tokens = []\n+            while i < len(tokens) and tokens[i] != ';':\n+                # store as lower-case if not a number literal (but we need to preserve the literal? Actually numbers can be processed either way.)\n+                # However, since our language is case-insensitive and numbers are treated as numbers regardless,\n+                # it's safe to lower-case everything that is not numeric. But to be safe, we just lower-case all tokens.\n+                definition_tokens.append(tokens[i])\n+                i += 1\n+            if i >= len(tokens) or tokens[i] != ';':\n+                raise ValueError('Invalid definition: missing \";\"')\n+            # store definition tokens in dictionary\n+            definitions[word_name] = [t for t in definition_tokens]\n+            i += 1  # Skip the ';'\n+        else:\n+            processed_tokens.append(token)\n+            i += 1\n+\n+    # Now, process the main token stream with definitions expanded.\n+    # We'll use a list as our token queue. We'll insert definitions tokens at the beginning when a custom word is encountered.\n+    # Convert tokens to lower-case for non-number tokens during evaluation.\n+    token_queue = processed_tokens\n+    stack = []\n+\n+    # Define built-in operations in a dictionary mapping token to a function\n+    def add():\n+        if len(stack) < 2:\n+            raise StackUnderflowError(\"Insufficient number of items in stack for '+' operation\")\n+        b = stack.pop()\n+        a = stack.pop()\n+        stack.append(a + b)\n+\n+    def subtract():\n+        if len(stack) < 2:\n+            raise StackUnderflowError(\"Insufficient number of items in stack for '-' operation\")\n+        b = stack.pop()\n+        a = stack.pop()\n+        stack.append(a - b)\n+\n+    def multiply():\n+        if len(stack) < 2:\n+            raise StackUnderflowError(\"Insufficient number of items in stack for '*' operation\")\n+        b = stack.pop()\n+        a = stack.pop()\n+        stack.append(a * b)\n+\n+    def divide():\n+        if len(stack) < 2:\n+            raise StackUnderflowError(\"Insufficient number of items in stack for '/' operation\")\n+        b = stack.pop()\n+        a = stack.pop()\n+        if b == 0:\n+            raise ZeroDivisionError(\"divide by zero\")\n+        # Forth typically uses integer division that truncates toward zero\n+        # Python's int division using // floors towards negative infinity, so we mimic truncation towards zero\n+        # We can do: int(a / b) since a and b are ints\n+        result = int(a / b)\n+        stack.append(result)\n+\n+    def dup():\n+        if len(stack) < 1:\n+            raise StackUnderflowError(\"Insufficient number of items in stack for DUP\")\n+        stack.append(stack[-1])\n+\n+    def drop():\n+        if len(stack) < 1:\n+            raise StackUnderflowError(\"Insufficient number of items in stack for DROP\")\n+        stack.pop()\n+\n+    def swap():\n+        if len(stack) < 2:\n+            raise StackUnderflowError(\"Insufficient number of items in stack for SWAP\")\n+        stack[-1], stack[-2] = stack[-2], stack[-1]\n+\n+    def over():\n+        if len(stack) < 2:\n+            raise StackUnderflowError(\"Insufficient number of items in stack for OVER\")\n+        stack.append(stack[-2])\n+\n+    builtins = {\n+        '+': add,\n+        '-': subtract,\n+        '*': multiply,\n+        '/': divide,\n+        'dup': dup,\n+        'drop': drop,\n+        'swap': swap,\n+        'over': over\n+    }\n+\n+    # Process tokens:\n+    # We'll iterate as long as there are tokens in token_queue.\n+    # For each token, if it's a number, push to stack. If it's an operator, perform the operation.\n+    # If it's a custom word, expand it.\n+    while token_queue:\n+        token = token_queue.pop(0)\n+        # Try to interpret the token as an integer.\n+        # A token is a number if it represents a valid integer literal (we use int conversion).\n+        try:\n+            # If the token starts with '-' or digit, try integer parse.\n+            # But note: a token like '-3' is valid.\n+            value = int(token)\n+            stack.append(value)\n+            continue\n+        except ValueError:\n+            pass\n+\n+        token_lower = token.lower()\n+        if token_lower in builtins:\n+            # execute the built-in operation\n+            builtins[token_lower]()\n+        elif token_lower in definitions:\n+            # expand custom definition: insert its tokens at the front of the token queue.\n+            # We preserve order: the first token in the definition should be executed first.\n+            # Create a copy of the definition tokens.\n+            definition_tokens = definitions[token_lower][:]\n+            # We need to insert these tokens to the beginning of the queue\n+            token_queue = definition_tokens + token_queue\n+        else:\n+            # if token is not recognized, it's an undefined operation.\n+            # Raise a ValueError with a meaningful message.\n+            raise ValueError(f\"undefined operation: {token}\")\n+\n+    return stack\n", "proposed_model_patches": [], "eval_result": "unresolved", "success": true}