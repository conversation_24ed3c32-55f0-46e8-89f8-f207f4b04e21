{"instance_id": "javascript__pig-latin", "model_name_or_path": "20250510_021328--claude-3-5-sonnet-20241022_0", "model_patch": "diff --git a/pig-latin.js b/pig-latin.js\nindex 66ca7b0..f8bc8e3 100644\n--- a/pig-latin.js\n+++ b/pig-latin.js\n@@ -1,8 +1,66 @@\n-//\n-// This is only a SKELETON file for the 'Pig Latin' exercise. It's been provided as a\n-// convenience to get you started writing code faster.\n-//\n+/*\n+Pig Latin Translator Implementation\n \n-export const translate = () => {\n-  throw new Error('Remove this statement and implement this function');\n+Rules:\n+1. If a word begins with a vowel, or starts with \"xr\" or \"yt\", simply append \"ay\".\n+2. If a word begins with one or more consonants, move them to the end then append \"ay\".\n+3. If a word starts with zero or more consonants followed by \"qu\", move the consonants (if any) and the \"qu\" together, then append \"ay\".\n+4. If a word starts with one or more consonants followed by \"y\", move the consonants before the \"y\" to the end, then append \"ay\".\n+\n+Assumptions:\n+- Input may be a sentence containing multiple words separated by spaces.\n+- Only letters are considered; punctuation handling is not specified.\n+*/\n+\n+// Helper function to check if character is a vowel\n+const isVowel = ch => 'aeiou'.includes(ch);\n+\n+// Translate a single word into Pig Latin\n+const translateWord = word => {\n+  // rule 1: if word begins with a vowel or starts with \"xr\" or \"yt\"\n+  if (/^(?:[aeiou]|xr|yt)/.test(word)) {\n+    return word + \"ay\";\n+  }\n+\n+  let index = 0;\n+  const length = word.length;\n+\n+  while (index < length) {\n+    // Special handling for \"qu\" sequence\n+    if (word[index] === 'q' && word[index + 1] === 'u') {\n+      index += 2;\n+      continue; // continue to check if there are additional consonant clusters? Actually, rule says treat qu as part of the initial consonant sequence.\n+    }\n+    // If we're past the first letter and we hit a 'y', then treat it as a vowel boundary\n+    if (index > 0 && word[index] === 'y') {\n+      break;\n+    }\n+    // If the current letter is a vowel, break out\n+    if (isVowel(word[index])) {\n+      break;\n+    }\n+    index++;\n+  }\n+\n+  // Now, slice the word into the moved part and the remainder\n+  let head = word.slice(0, index);\n+  let tail = word.slice(index);\n+  return tail + head + \"ay\";\n+};\n+\n+// Main translate function: translates an entire string\n+export const translate = text => {\n+  // Split the text by whitespace and translate each word individually\n+  // This simplistic approach assumes words are separated by spaces\n+  return text\n+    .split(/(\\s+)/)  // Keep the whitespace so that formatting is retained\n+    .map(chunk => {\n+      // Only transform chunks that are non-whitespace\n+      if (/\\S/.test(chunk)) {\n+        return translateWord(chunk);\n+      } else {\n+        return chunk;\n+      }\n+    })\n+    .join(\"\");\n };\n", "proposed_model_patches": [], "eval_result": "resolved", "success": true}