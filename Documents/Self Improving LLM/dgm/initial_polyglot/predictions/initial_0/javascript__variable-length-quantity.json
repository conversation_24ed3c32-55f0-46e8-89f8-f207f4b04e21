{"instance_id": "javascript__variable-length-quantity", "model_name_or_path": "20250510_021328--claude-3-5-sonnet-20241022_0", "model_patch": "diff --git a/variable-length-quantity.js b/variable-length-quantity.js\nindex 62862ff..48b4c2c 100644\n--- a/variable-length-quantity.js\n+++ b/variable-length-quantity.js\n@@ -1,12 +1,101 @@\n-//\n-// This is only a SKELETON file for the 'Variable Length Quantity' exercise. It's been provided as a\n-// convenience to get you started writing code faster.\n-//\n+/*\n+ * Variable Length Quantity Encoding/Decoding\n+ *\n+ * Implements variable length quantity encoding and decoding for 32-bit unsigned integers.\n+ *\n+ * The encoding splits a number into 7-bit groups. All groups except the last have bit 7 set.\n+ * For example:\n+ *\n+ * 0x00000000 -> [0x00]\n+ * 0x00000040 -> [0x40]\n+ * 0x0000007F -> [0x7F]\n+ * 0x00000080 -> [0x81, 0x00]\n+ * 0x00002000 -> [0xC0, 0x00]\n+ * 0x00003FFF -> [0xFF, 0x7F]\n+ * 0x00004000 -> [0x81, 0x80, 0x00]\n+ * 0x00100000 -> [0xC0, 0x80, 0x00]\n+ * 0x001FFFFF -> [0xFF, 0xFF, 0x7F]\n+ * 0x00200000 -> [0x81, 0x80, 0x80, 0x00]\n+ * 0x08000000 -> [0xC0, 0x80, 0x80, 0x00]\n+ * 0x0FFFFFFF -> [0xFF, 0xFF, 0xFF, 0x7F]\n+ *\n+ * For this exercise, we assume numbers fit into a 32-bit unsigned integer. Typically, encode and decode\n+ * work on arrays of numbers (for encode, an array of integers to be encoded, and for decode, an array of\n+ * bytes representing VLQ-encoded integers).\n+ *\n+ * Error handling:\n+ * - For encode, negative numbers or numbers > 0xFFFFFFFF will throw a RangeError.\n+ * - For decode, if a sequence is incomplete (i.e. a number's VLQ encoding does not end with a byte without the\n+ *   continuation bit set), an Error is thrown.\n+ * - Additionally, decode validates that each byte is in the 0-255 range.\n+ */\n \n-export const encode = () => {\n-  throw new Error('Remove this statement and implement this function');\n+export const encode = (numbers) => {\n+  if (!Array.isArray(numbers)) {\n+    throw new TypeError('Input must be an array');\n+  }\n+  \n+  const result = [];\n+  \n+  for (const number of numbers) {\n+    if (typeof number !== 'number' || number < 0 || number > 0xFFFFFFFF) {\n+      throw new RangeError('Number out of range');\n+    }\n+\n+    let n = number;\n+    const bytes = [];\n+    \n+    // Special case: 0 must be encoded as [0x00]\n+    do {\n+      bytes.push(n & 0x7F);\n+      n >>>= 7;\n+    } while (n > 0);\n+    \n+    // Reverse to get most significant byte first\n+    bytes.reverse();\n+    \n+    // Set the continuation bit on all but the last byte\n+    for (let i = 0; i < bytes.length - 1; i++) {\n+      bytes[i] |= 0x80;\n+    }\n+    \n+    result.push(...bytes);\n+  }\n+  \n+  return result;\n };\n \n-export const decode = () => {\n-  throw new Error('Remove this statement and implement this function');\n+export const decode = (bytes) => {\n+  if (!Array.isArray(bytes)) {\n+    throw new TypeError('Input must be an array');\n+  }\n+  \n+  const result = [];\n+  let current = 0;\n+  let inProgress = false;\n+  \n+  for (let i = 0; i < bytes.length; i++) {\n+    const byte = bytes[i];\n+    \n+    if (typeof byte !== 'number' || byte < 0 || byte > 0xFF) {\n+      throw new RangeError('Byte out of range');\n+    }\n+    \n+    // Incorporate the 7 bits into current number\n+    current = (current << 7) | (byte & 0x7F);\n+    inProgress = true;\n+    \n+    // If continuation bit is not set, current integer is complete\n+    if ((byte & 0x80) === 0) {\n+      result.push(current);\n+      current = 0;\n+      inProgress = false;\n+    }\n+  }\n+  \n+  if (inProgress) {\n+    throw new Error('Incomplete sequence');\n+  }\n+  \n+  return result;\n };\n", "proposed_model_patches": [], "eval_result": "unresolved", "success": true}