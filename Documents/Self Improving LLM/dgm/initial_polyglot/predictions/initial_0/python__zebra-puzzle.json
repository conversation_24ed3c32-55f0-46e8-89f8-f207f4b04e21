{"instance_id": "python__zebra-puzzle", "model_name_or_path": "20250510_021328--claude-3-5-sonnet-20241022_0", "model_patch": "diff --git a/zebra_puzzle.py b/zebra_puzzle.py\nindex 2d78762..0c2b202 100644\n--- a/zebra_puzzle.py\n+++ b/zebra_puzzle.py\n@@ -1,6 +1,153 @@\n+import itertools\n+\n+def solve():\n+    colors = ['red', 'green', 'ivory', 'yellow', 'blue']\n+    nationalities = ['Englishman', 'Spaniard', 'Ukrainian', 'Norwegian', 'Japanese']\n+    pets = ['dog', 'snail', 'fox', 'horse', 'zebra']\n+    beverages = ['coffee', 'tea', 'milk', 'orange juice', 'water']\n+    hobbies = ['dancing', 'painting', 'reading', 'football', 'chess']\n+\n+    for perm_colors in itertools.permutations(colors):\n+        for perm_nat in itertools.permutations(nationalities):\n+            # Clue 10: The Norwegian lives in the first house.\n+            if perm_nat[0] != 'Norwegian':\n+                continue\n+            for perm_pets in itertools.permutations(pets):\n+                # Clue 3: The Spaniard owns the dog.\n+                if not any(nat == 'Spaniard' and pet == 'dog' for nat, pet in zip(perm_nat, perm_pets)):\n+                    continue\n+                for perm_bev in itertools.permutations(beverages):\n+                    # Clue 9: The person in the middle house drinks milk.\n+                    if perm_bev[2] != 'milk':\n+                        continue\n+                    for perm_hobby in itertools.permutations(hobbies):\n+                        valid = True\n+                        # Clue 2: The Englishman lives in the red house.\n+                        for i in range(5):\n+                            if perm_nat[i] == 'Englishman' and perm_colors[i] != 'red':\n+                                valid = False\n+                                break\n+                        if not valid:\n+                            continue\n+\n+                        # Clue 4: The person in the green house drinks coffee.\n+                        for i in range(5):\n+                            if perm_colors[i] == 'green' and perm_bev[i] != 'coffee':\n+                                valid = False\n+                                break\n+                        if not valid:\n+                            continue\n+\n+                        # Clue 5: The Ukrainian drinks tea.\n+                        for i in range(5):\n+                            if perm_nat[i] == 'Ukrainian' and perm_bev[i] != 'tea':\n+                                valid = False\n+                                break\n+                        if not valid:\n+                            continue\n+\n+                        # Clue 6: The green house is immediately to the right of the ivory house.\n+                        if not any(i > 0 and perm_colors[i] == 'green' and perm_colors[i-1] == 'ivory' for i in range(5)):\n+                            continue\n+\n+                        # Clue 7: The snail owner likes to go dancing.\n+                        for i in range(5):\n+                            if perm_pets[i] == 'snail' and perm_hobby[i] != 'dancing':\n+                                valid = False\n+                                break\n+                        if not valid:\n+                            continue\n+\n+                        # Clue 8: The person in the yellow house is a painter.\n+                        for i in range(5):\n+                            if perm_colors[i] == 'yellow' and perm_hobby[i] != 'painting':\n+                                valid = False\n+                                break\n+                        if not valid:\n+                            continue\n+\n+                        # Clue 11: The person who enjoys reading lives in the house next to the person with the fox.\n+                        for i in range(5):\n+                            if perm_hobby[i] == 'reading':\n+                                neighbors = []\n+                                if i > 0:\n+                                    neighbors.append(perm_pets[i-1])\n+                                if i < 4:\n+                                    neighbors.append(perm_pets[i+1])\n+                                if 'fox' not in neighbors:\n+                                    valid = False\n+                                    break\n+                        if not valid:\n+                            continue\n+\n+                        # Clue 12: The painter's house is next to the house with the horse.\n+                        for i in range(5):\n+                            if perm_hobby[i] == 'painting':\n+                                neighbors = []\n+                                if i > 0:\n+                                    neighbors.append(perm_pets[i-1])\n+                                if i < 4:\n+                                    neighbors.append(perm_pets[i+1])\n+                                if 'horse' not in neighbors:\n+                                    valid = False\n+                                    break\n+                        if not valid:\n+                            continue\n+\n+                        # Clue 13: The person who plays football drinks orange juice.\n+                        for i in range(5):\n+                            if perm_hobby[i] == 'football' and perm_bev[i] != 'orange juice':\n+                                valid = False\n+                                break\n+                        if not valid:\n+                            continue\n+\n+                        # Clue 14: The Japanese person plays chess.\n+                        for i in range(5):\n+                            if perm_nat[i] == 'Japanese' and perm_hobby[i] != 'chess':\n+                                valid = False\n+                                break\n+                        if not valid:\n+                            continue\n+\n+                        # Clue 15: The Norwegian lives next to the blue house.\n+                        for i in range(5):\n+                            if perm_nat[i] == 'Norwegian':\n+                                neighbors = []\n+                                if i > 0:\n+                                    neighbors.append(perm_colors[i-1])\n+                                if i < 4:\n+                                    neighbors.append(perm_colors[i+1])\n+                                if 'blue' not in neighbors:\n+                                    valid = False\n+                                    break\n+                        if not valid:\n+                            continue\n+\n+                        # If we reached here, we found a valid solution!\n+                        water_drinker = None\n+                        zebra_owner = None\n+                        for i in range(5):\n+                            if perm_bev[i] == 'water':\n+                                water_drinker = perm_nat[i]\n+                            if perm_pets[i] == 'zebra':\n+                                zebra_owner = perm_nat[i]\n+                        return water_drinker, zebra_owner\n+    return None, None\n+\n+\n def drinks_water():\n-    pass\n+    \"\"\"Return the nationality of the resident who drinks water.\"\"\"\n+    return solve()[0]\n \n \n def owns_zebra():\n-    pass\n+    \"\"\"Return the nationality of the resident who owns the zebra.\"\"\"\n+    return solve()[1]\n+\n+\n+if __name__ == '__main__':\n+    water = drinks_water()\n+    zebra = owns_zebra()\n+    print(f\"The {water} drinks water.\")\n+    print(f\"The {zebra} owns the zebra.\")\n", "proposed_model_patches": [], "eval_result": "unresolved", "success": true}