{"instance_id": "javascript__promises", "model_name_or_path": "20250510_021328--claude-3-5-sonnet-20241022_0", "model_patch": "diff --git a/promises.js b/promises.js\nindex 5d0fbb1..7d30fc5 100644\n--- a/promises.js\n+++ b/promises.js\n@@ -3,22 +3,96 @@\n // convenience to get you started writing code faster.\n //\n \n-export const promisify = () => {\n-  throw new Error('Remove this statement and implement this function');\n+export const promisify = (fn) => {\n+  return (...args) => {\n+    return new Promise((resolve, reject) => {\n+      fn(...args, (error, data) => {\n+        if (error) {\n+          reject(error);\n+        } else {\n+          resolve(data);\n+        }\n+      });\n+    });\n+  };\n };\n \n-export const all = () => {\n-  throw new Error('Remove this statement and implement this function');\n+export const all = (promises) => {\n+  return new Promise((resolve, reject) => {\n+    if (promises.length === 0) {\n+      return resolve([]);\n+    }\n+    const results = new Array(promises.length);\n+    let completed = 0;\n+    promises.forEach((p, i) => {\n+      Promise.resolve(p)\n+        .then((value) => {\n+          results[i] = value;\n+          completed++;\n+          if (completed === promises.length) {\n+            resolve(results);\n+          }\n+        })\n+        .catch(reject);\n+    });\n+  });\n };\n \n-export const allSettled = () => {\n-  throw new Error('Remove this statement and implement this function');\n+export const allSettled = (promises) => {\n+  return new Promise((resolve) => {\n+    if (promises.length === 0) {\n+      return resolve([]);\n+    }\n+    const results = new Array(promises.length);\n+    let completed = 0;\n+    promises.forEach((p, i) => {\n+      Promise.resolve(p)\n+        .then((value) => {\n+          results[i] = { status: 'fulfilled', value };\n+        })\n+        .catch((reason) => {\n+          results[i] = { status: 'rejected', reason };\n+        })\n+        .finally(() => {\n+          completed++;\n+          if (completed === promises.length) {\n+            resolve(results);\n+          }\n+        });\n+    });\n+  });\n };\n \n-export const race = () => {\n-  throw new Error('Remove this statement and implement this function');\n+export const race = (promises) => {\n+  return new Promise((resolve, reject) => {\n+    if (promises.length === 0) {\n+      // never resolve or reject if empty\n+      return;\n+    }\n+    promises.forEach((p) => {\n+      Promise.resolve(p).then(resolve, reject);\n+    });\n+  });\n };\n \n-export const any = () => {\n-  throw new Error('Remove this statement and implement this function');\n+export const any = (promises) => {\n+  return new Promise((resolve, reject) => {\n+    if (promises.length === 0) {\n+      // if no promises, reject immediately with an AggregateError\n+      return reject(new AggregateError([], 'All promises were rejected'));\n+    }\n+    const errors = [];\n+    let rejectedCount = 0;\n+    promises.forEach((p, i) => {\n+      Promise.resolve(p)\n+        .then(resolve)\n+        .catch((err) => {\n+          errors[i] = err;\n+          rejectedCount++;\n+          if (rejectedCount === promises.length) {\n+            reject(new AggregateError(errors, 'All promises were rejected'));\n+          }\n+        });\n+    });\n+  });\n };\n", "proposed_model_patches": [], "eval_result": "unresolved", "success": true}