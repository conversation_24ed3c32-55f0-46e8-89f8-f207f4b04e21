{"instance_id": "rust__acronym", "model_name_or_path": "20250510_021328--claude-3-5-sonnet-20241022_0", "model_patch": "diff --git a/src/lib.rs b/src/lib.rs\nindex d31dd96..67aefb4 100644\n--- a/src/lib.rs\n+++ b/src/lib.rs\n@@ -1,3 +1,26 @@\n pub fn abbreviate(phrase: &str) -> String {\n-    todo!(\"Given the phrase '{phrase}', return its acronym\");\n+    // Replace hyphens with spaces and remove other punctuation\n+    let cleaned: String = phrase.chars()\n+        .map(|c| {\n+            if c == '-' {\n+                ' '\n+            } else if c.is_alphanumeric() || c.is_whitespace() {\n+                c\n+            } else {\n+                // remove punctuation characters\n+                ' '\n+            }\n+        })\n+        .collect();\n+    \n+    // Split the cleaned string into words\n+    let words = cleaned.split_whitespace();\n+\n+    // Collect the first letter of each word, convert to uppercase\n+    let acronym: String = words\n+        .filter_map(|word| word.chars().next())\n+        .map(|c| c.to_ascii_uppercase())\n+        .collect();\n+    \n+    acronym\n }\n", "proposed_model_patches": [], "eval_result": "unresolved", "success": true}