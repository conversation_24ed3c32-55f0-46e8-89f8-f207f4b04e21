{"instance_id": "javascript__rest-api", "model_name_or_path": "20250510_021328--claude-3-5-sonnet-20241022_0", "model_patch": "diff --git a/rest-api.js b/rest-api.js\nindex bddc9c0..b7bd3d8 100644\n--- a/rest-api.js\n+++ b/rest-api.js\n@@ -1,18 +1,148 @@\n-//\n-// This is only a SKELETON file for the 'Rest API' exercise. It's been provided as a\n-// convenience to get you started writing code faster.\n-//\n+/*\n+Rest API for tracking IOUs between roommates.\n+\n+API Specification:\n+\n+GET /users\n+  - If query parameter 'users' is provided (e.g., /users?users=<PERSON>,<PERSON>), return the specified users (sorted by name).\n+  - Else, return all users (sorted by name).\n+\n+POST /add\n+  - Payload: {\"user\": \"<username>\"}\n+  - Creates a new user with no debts. Returns the created user object.\n+\n+POST /iou\n+  - Payload: {\"lender\": \"<lender>\", \"borrower\": \"<borrower>\", \"amount\": <number>}\n+  - Updates the debts between lender and borrower and returns updated user objects for both.\n+\n+Each User object is structured as follows:\n+{\n+  name: string,\n+  owes: { [otherUserName]: number, ... },\n+  owed_by: { [otherUserName]: number, ... },\n+  balance: number\n+}\n+\n+Balance is calculated as (sum of owed_by values) - (sum of owes values).\n+The IOU transactions cancel out any cross-debts when possible.\n+*/\n \n export class RestAPI {\n-  constructor() {\n-    throw new Error('Remove this statement and implement this function');\n+  constructor(database) {\n+    // Database is an object mapping user names to user objects.\n+    // If no database provided, initialize an empty one.\n+    this.db = database || {};\n+  }\n+\n+  // Helper function to recalc balance for a user\n+  calcBalance(user) {\n+    const totalOwedBy = Object.values(user.owed_by).reduce((a, b) => a + b, 0);\n+    const totalOwes = Object.values(user.owes).reduce((a, b) => a + b, 0);\n+    user.balance = parseFloat((totalOwedBy - totalOwes).toFixed(2));\n+  }\n+\n+  // Helper to return a sorted array of user objects (by name)\n+  getSortedUsers(userNames) {\n+    let users = userNames.map(name => this.db[name]);\n+    users.sort((a, b) => a.name.localeCompare(b.name));\n+    return users;\n   }\n \n   get(url) {\n-    throw new Error('Remove this statement and implement this function');\n+    // Only support GET for /users endpoint\n+    if (!url.startsWith('/users')) {\n+      return {};\n+    }\n+    // Parse query string if exists\n+    let query = {};\n+    const parts = url.split('?');\n+    if (parts.length > 1) {\n+      const queryString = parts[1];\n+      // naive query param parsing: assume query string format is key=value\n+      queryString.split('&').forEach(pair => {\n+        const [key, value] = pair.split('=');\n+        query[key] = value;\n+      });\n+    }\n+\n+    if (query.users) {\n+      const requested = query.users.split(',');\n+      // Filter out only users that exist\n+      const validUsers = requested.filter(name => this.db.hasOwnProperty(name));\n+      return { users: this.getSortedUsers(validUsers) };\n+    } else {\n+      // return all users\n+      const allUserNames = Object.keys(this.db);\n+      return { users: this.getSortedUsers(allUserNames) };\n+    }\n   }\n \n   post(url, payload) {\n-    throw new Error('Remove this statement and implement this function');\n+    if (url === '/add') {\n+      const userName = payload.user;\n+      // Only add user if not already present\n+      if (!this.db.hasOwnProperty(userName)) {\n+        const newUser = {\n+          name: userName,\n+          owes: {},\n+          owed_by: {},\n+          balance: 0\n+        };\n+        this.db[userName] = newUser;\n+      }\n+      return this.db[userName];\n+    } else if (url === '/iou') {\n+      const lenderName = payload.lender;\n+      const borrowerName = payload.borrower;\n+      let amount = parseFloat(payload.amount);\n+      const lender = this.db[lenderName];\n+      const borrower = this.db[borrowerName];\n+\n+      // First, check if there is any reverse debt that can be canceled out.\n+      // That is, if lender owes borrower, then that debt can be reduced.\n+      if (lender.owes.hasOwnProperty(borrowerName)) {\n+        const debt = lender.owes[borrowerName];\n+        if (debt >= amount) {\n+          lender.owes[borrowerName] = parseFloat((debt - amount).toFixed(2));\n+          if (lender.owes[borrowerName] === 0) {\n+            delete lender.owes[borrowerName];\n+          }\n+          // Corresponding update for borrower.owed_by\n+          borrower.owed_by[lenderName] = parseFloat((borrower.owed_by[lenderName] - amount).toFixed(2));\n+          if (borrower.owed_by[lenderName] === 0) {\n+            delete borrower.owed_by[lenderName];\n+          }\n+          amount = 0;\n+        } else {\n+          amount = parseFloat((amount - debt).toFixed(2));\n+          delete lender.owes[borrowerName];\n+          delete borrower.owed_by[lenderName];\n+        }\n+      }\n+\n+      // Now, if there's still amount left, update lender's owed_by and borrower's owes\n+      if (amount > 0) {\n+        if (lender.owed_by.hasOwnProperty(borrowerName)) {\n+          lender.owed_by[borrowerName] = parseFloat((lender.owed_by[borrowerName] + amount).toFixed(2));\n+        } else {\n+          lender.owed_by[borrowerName] = amount;\n+        }\n+        if (borrower.owes.hasOwnProperty(lenderName)) {\n+          borrower.owes[lenderName] = parseFloat((borrower.owes[lenderName] + amount).toFixed(2));\n+        } else {\n+          borrower.owes[lenderName] = amount;\n+        }\n+      }\n+\n+      // Recalculate balances for both users\n+      this.calcBalance(lender);\n+      this.calcBalance(borrower);\n+\n+      // Return the updated users in alphabetical order\n+      const updatedUsers = [lenderName, borrowerName].sort().map(name => this.db[name]);\n+      return { users: updatedUsers };\n+    } else {\n+      return {};\n+    }\n   }\n }\n", "proposed_model_patches": [], "eval_result": "unresolved", "success": true}