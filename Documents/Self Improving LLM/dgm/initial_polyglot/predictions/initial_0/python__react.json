{"instance_id": "python__react", "model_name_or_path": "20250510_021328--claude-3-5-sonnet-20241022_0", "model_patch": "diff --git a/react.py b/react.py\nindex ab6be31..5c28f60 100644\n--- a/react.py\n+++ b/react.py\n@@ -1,15 +1,53 @@\n class InputCell:\n     def __init__(self, initial_value):\n-        self.value = None\n+        self.value = initial_value\n+        self.children = set()\n+\n+    def set_value(self, new_value):\n+        if new_value != self.value:\n+            self.value = new_value\n+            # propagate changes to children\n+            for child in list(self.children):\n+                child._update()\n \n \n class ComputeCell:\n     def __init__(self, inputs, compute_function):\n-        self.value = None\n+        self.inputs = inputs\n+        self.compute_function = compute_function\n+        self.children = set()\n+        self.callbacks = []\n+\n+        # Register self as a dependent to each input cell\n+        for cell in self.inputs:\n+            cell.children.add(self)\n+\n+        # initial computation without triggering callbacks\n+        self.value = self._compute_value()\n+\n+    def _compute_value(self):\n+        # compute new value from input cell values\n+        # compute_function is expected to accept a list of values\n+        input_values = [cell.value for cell in self.inputs]\n+        return self.compute_function(input_values)\n+\n+    def _update(self):\n+        new_value = self._compute_value()\n+        if new_value != self.value:\n+            self.value = new_value\n+            # call callbacks to notify of change\n+            for callback in self.callbacks:\n+                callback(self.value)\n+            # propagate the update to children\n+            for child in list(self.children):\n+                child._update()\n \n     def add_callback(self, callback):\n-        pass\n+        # Register a new callback to be notified when value changes\n+        if callback not in self.callbacks:\n+            self.callbacks.append(callback)\n \n     def remove_callback(self, callback):\n-        pass\n-    \n\\ No newline at end of file\n+        # Unregister an existing callback\n+        if callback in self.callbacks:\n+            self.callbacks.remove(callback)\n", "proposed_model_patches": [], "eval_result": "unresolved", "success": true}