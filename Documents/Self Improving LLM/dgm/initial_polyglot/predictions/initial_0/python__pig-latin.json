{"instance_id": "python__pig-latin", "model_name_or_path": "20250510_021328--claude-3-5-sonnet-20241022_0", "model_patch": "diff --git a/pig_latin.py b/pig_latin.py\nindex dda3f07..3c9f1eb 100644\n--- a/pig_latin.py\n+++ b/pig_latin.py\n@@ -1,2 +1,44 @@\n def translate(text):\n-    pass\n+    def translate_word(word):\n+        vowels = \"aeiou\"\n+        # Rule 1: if word starts with a vowel, or starts with 'xr' or 'yt'\n+        if word.startswith((\"xr\", \"yt\")) or word[0] in vowels:\n+            return word + \"ay\"\n+        \n+        idx = 0\n+        # Process the word to find the position where the initial consonant cluster ends\n+        while idx < len(word):\n+            # Rule 3: if 'qu' is encountered, include it in the consonant cluster\n+            if word[idx:idx+2] == \"qu\":\n+                idx += 2\n+                break\n+            # Rule 4: if we encounter a 'y' after some consonants, that's where we split\n+            if word[idx] == \"y\" and idx > 0:\n+                break\n+            # If the current letter is a vowel, we stop\n+            if word[idx] in vowels:\n+                break\n+            idx += 1\n+\n+        return word[idx:] + word[:idx] + \"ay\"\n+    \n+    # Translate each word separately and join them with spaces\n+    return \" \".join(translate_word(word) for word in text.split())\n+\n+\n+if __name__ == '__main__':\n+    # Some quick tests\n+    test_cases = [\n+        \"apple\",\n+        \"xray\",\n+        \"yttria\",\n+        \"pig\",\n+        \"chair\",\n+        \"thrush\",\n+        \"quick\",\n+        \"square\",\n+        \"my\",\n+        \"rhythm\"\n+    ]\n+    for case in test_cases:\n+        print(f\"{case} -> {translate(case)}\")\n", "proposed_model_patches": [], "eval_result": "resolved", "success": true}