{"instance_id": "javascript__bottle-song", "model_name_or_path": "20250510_021328--claude-3-5-sonnet-20241022_0", "model_patch": "diff --git a/bottle-song.js b/bottle-song.js\nindex 9ca0344..448105e 100644\n--- a/bottle-song.js\n+++ b/bottle-song.js\n@@ -1,8 +1,64 @@\n-//\n-// This is only a SKELETON file for the 'Bottle Song' exercise. It's been provided as a\n-// convenience to get you started writing code faster.\n-//\n+/*\n+ * This is the implementation for the 'Bottle Song' exercise.\n+ * The function recite generates the verses for the \"Ten Green Bottles\" song.\n+ * It takes two parameters:\n+ *    initialBottlesCount: the starting number of bottles (e.g., 10)\n+ *    takeDownCount: the number of verses to recite (each verse takes one bottle down).\n+ *\n+ * Each verse follows the pattern:\n+ * <current> green bottle(s) hanging on the wall,\n+ * <current> green bottle(s) hanging on the wall,\n+ * And if one green bottle should accidentally fall,\n+ * There'll be <next> green bottle(s) hanging on the wall.\n+ *\n+ * Special handling is required for singular/plural and for zero bottles:\n+ * - When there is 1 bottle, use \"One green bottle\" (singular) instead of \"bottles\".\n+ * - When the next count is 0, use \"no green bottles\" (all lowercase) in the final line.\n+ */\n+\n+const numberWords = {\n+  10: 'Ten',\n+  9: 'Nine',\n+  8: 'Eight',\n+  7: 'Seven',\n+  6: 'Six',\n+  5: 'Five',\n+  4: 'Four',\n+  3: 'Three',\n+  2: 'Two',\n+  1: 'One'\n+};\n+\n+// Helper function to generate the phrase for a given number of bottles.\n+// isStarting indicates whether the phrase should be capitalized (for the beginning of a line).\n+function bottlePhrase(n, isStarting) {\n+  if (n === 0) {\n+    return 'no green bottles';\n+  } else if (n === 1) {\n+    return isStarting ? 'One green bottle' : 'one green bottle';\n+  } else {\n+    // Use mapping if available, else default to number string\n+    const word = numberWords[n] || n.toString();\n+    return word + ' green bottles';\n+  }\n+}\n \n export const recite = (initialBottlesCount, takeDownCount) => {\n-  throw new Error('Remove this statement and implement this function');\n+  let verses = [];\n+  for (let i = 0; i < takeDownCount; i++) {\n+    const current = initialBottlesCount - i;\n+    const next = current - 1;\n+\n+    // If current is less than 1, no more verses can be recited.\n+    if (current < 1) break;\n+\n+    const line1 = `${bottlePhrase(current, true)} hanging on the wall,`;\n+    const line2 = `${bottlePhrase(current, true)} hanging on the wall,`;\n+    const line3 = 'And if one green bottle should accidentally fall,';\n+    const line4 = `There'll be ${bottlePhrase(next, false)} hanging on the wall.`;\n+\n+    verses.push([line1, line2, line3, line4].join('\\n'));\n+  }\n+\n+  return verses.join('\\n\\n');\n };\n", "proposed_model_patches": [], "eval_result": "unresolved", "success": true}