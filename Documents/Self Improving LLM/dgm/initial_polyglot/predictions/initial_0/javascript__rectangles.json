{"instance_id": "javascript__rectangles", "model_name_or_path": "20250510_021328--claude-3-5-sonnet-20241022_0", "model_patch": "diff --git a/rectangles.js b/rectangles.js\nindex 332cbbd..16e349b 100644\n--- a/rectangles.js\n+++ b/rectangles.js\n@@ -1,8 +1,67 @@\n //\n-// This is only a SKELETON file for the 'Rectangles' exercise. It's been provided as a\n-// convenience to get you started writing code faster.\n+// This file provides an implementation for the 'Rectangles' exercise.\n+// Given an ASCII diagram, count the number of rectangles present.\n+// A rectangle is defined by four corners '+' with horizontal segments (- or +) and vertical segments (| or +).\n //\n+// The function expects a single string parameter, containing the ASCII diagram.\n+// Each line in the string should have the same length.\n \n-export function count() {\n-  throw new Error('Remove this statement and implement this function');\n+export function count(diagram) {\n+  // Split the diagram into rows\n+  const rows = diagram.split('\\n');\n+  const height = rows.length;\n+  if (height === 0) return 0;\n+  const width = rows[0].length;\n+\n+  let rectangles = 0;\n+\n+  // Helper: check horizontal edge between (row, colStart) and (row, colEnd) (exclusive of endpoints)\n+  function validHorizontal(row, colStart, colEnd) {\n+    for (let col = colStart + 1; col < colEnd; col++) {\n+      const c = rows[row][col];\n+      if (c !== '-' && c !== '+') {\n+        return false;\n+      }\n+    }\n+    return true;\n+  }\n+\n+  // Helper: check vertical edge between (rowStart, col) and (rowEnd, col) (exclusive endpoints)\n+  function validVertical(col, rowStart, rowEnd) {\n+    for (let row = rowStart + 1; row < rowEnd; row++) {\n+      const c = rows[row][col];\n+      if (c !== '|' && c !== '+') {\n+        return false;\n+      }\n+    }\n+    return true;\n+  }\n+\n+  // Iterate over all possible pairs of rows and pairs of columns\n+  for (let top = 0; top < height; top++) {\n+    for (let bottom = top + 1; bottom < height; bottom++) {\n+      for (let left = 0; left < width; left++) {\n+        for (let right = left + 1; right < width; right++) {\n+          // Check if all four corners are '+'\n+          if (rows[top][left] !== '+' || rows[top][right] !== '+' ||\n+              rows[bottom][left] !== '+' || rows[bottom][right] !== '+') {\n+            continue;\n+          }\n+          // Check top horizontal edge\n+          if (!validHorizontal(top, left, right)) continue;\n+          // Check bottom horizontal edge\n+          if (!validHorizontal(bottom, left, right)) continue;\n+          // Check left vertical edge\n+          if (!validVertical(left, top, bottom)) continue;\n+          // Check right vertical edge\n+          if (!validVertical(right, top, bottom)) continue;\n+          \n+          // It forms a valid rectangle\n+          rectangles++;\n+        }\n+      }\n+    }\n+  }\n+\n+  return rectangles;\n }\n", "proposed_model_patches": [], "eval_result": "unresolved", "success": true}