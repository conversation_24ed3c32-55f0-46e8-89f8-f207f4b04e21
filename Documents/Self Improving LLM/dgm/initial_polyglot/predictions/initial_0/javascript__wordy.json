{"instance_id": "javascript__wordy", "model_name_or_path": "20250510_021328--claude-3-5-sonnet-20241022_0", "model_patch": "diff --git a/wordy.js b/wordy.js\nindex 9ee42c7..8d7e866 100644\n--- a/wordy.js\n+++ b/wordy.js\n@@ -1,8 +1,64 @@\n //\n-// This is only a SKELETON file for the 'Wordy' exercise. It's been provided as a\n-// convenience to get you started writing code faster.\n+// Wordy exercise implementation\n //\n \n-export const answer = () => {\n-  throw new Error('Remove this statement and implement this function');\n+export const answer = (question) => {\n+  if (typeof question !== 'string' || !question.startsWith('What is') || !question.endsWith('?')) {\n+    throw new Error('Invalid question');\n+  }\n+\n+  // Remove the \"What is\" prefix and the trailing question mark\n+  const content = question.slice(8, -1).trim();\n+  if (content.length === 0) {\n+    throw new Error('Syntax error in question');\n+  }\n+\n+  // Split by space. Note: some operations have two words (\"multiplied by\", \"divided by\")\n+  const tokens = content.split(' ');\n+\n+  // The first token should represent a number. It's possible that the number is negative.\n+  let result = parseInt(tokens[0], 10);\n+  if (isNaN(result)) {\n+    throw new Error('Syntax error in question');\n+  }\n+\n+  let i = 1;\n+  while (i < tokens.length) {\n+    let operator = tokens[i];\n+    let op;\n+    if (operator === 'plus') {\n+      op = (a, b) => a + b;\n+      i++;\n+    } else if (operator === 'minus') {\n+      op = (a, b) => a - b;\n+      i++;\n+    } else if (operator === 'multiplied') {\n+      // Expect next token to be 'by'\n+      if (tokens[i + 1] !== 'by') {\n+        throw new Error('Syntax error in question');\n+      }\n+      op = (a, b) => a * b;\n+      i += 2;\n+    } else if (operator === 'divided') {\n+      if (tokens[i + 1] !== 'by') {\n+        throw new Error('Syntax error in question');\n+      }\n+      op = (a, b) => a / b;\n+      i += 2;\n+    } else {\n+      throw new Error('Unsupported operation');\n+    }\n+\n+    if (i >= tokens.length) {\n+      throw new Error('Syntax error in question');\n+    }\n+    const number = parseInt(tokens[i], 10);\n+    if (isNaN(number)) {\n+      throw new Error('Syntax error in question');\n+    }\n+    result = op(result, number);\n+    i++;\n+  }\n+\n+  return result;\n };\n", "proposed_model_patches": [], "eval_result": "unresolved", "success": true}