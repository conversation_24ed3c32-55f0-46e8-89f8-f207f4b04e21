{"instance_id": "javascript__parallel-letter-frequency", "model_name_or_path": "20250510_021328--claude-3-5-sonnet-20241022_0", "model_patch": "diff --git a/parallel-letter-frequency.js b/parallel-letter-frequency.js\nindex bef5eb5..385840c 100644\n--- a/parallel-letter-frequency.js\n+++ b/parallel-letter-frequency.js\n@@ -1,8 +1,52 @@\n-//\n-// This is only a SKELETON file for the 'Parallel Letter Frequency' exercise. It's been provided as a\n-// convenience to get you started writing code faster.\n-//\n+// Import Worker and related functions from worker_threads\n+import { Worker, isMainThread, parentPort, workerData } from 'node:worker_threads';\n \n+// Helper function to compute frequency of letters in a given text\n+function letterFrequencies(text) {\n+  const freq = {};\n+  // Convert text to lowercase, and count only alphabet letters a-z\n+  for (const char of text.toLowerCase()) {\n+    if (char >= 'a' && char <= 'z') {\n+      freq[char] = (freq[char] || 0) + 1;\n+    }\n+  }\n+  return freq;\n+}\n+\n+// If this is a worker thread, perform the calculation and send result back\n+if (!isMainThread) {\n+  const result = letterFrequencies(workerData);\n+  parentPort.postMessage(result);\n+}\n+\n+// Main thread exports the parallelLetterFrequency function\n+// This function spawns a Worker for each text and aggregates the results\n export const parallelLetterFrequency = async (texts) => {\n-  throw new Error('Remove this statement and implement this function');\n+  // Create a Worker for each text using the current file as the worker script\n+  const workerPromises = texts.map(\n+    (text) =>\n+      new Promise((resolve, reject) => {\n+        const worker = new Worker(new URL(import.meta.url), { workerData: text });\n+        worker.on('message', resolve);\n+        worker.on('error', reject);\n+        worker.on('exit', (code) => {\n+          if (code !== 0) {\n+            reject(new Error(`Worker stopped with exit code ${code}`));\n+          }\n+        });\n+      })\n+  );\n+\n+  // Await all worker threads to complete\n+  const results = await Promise.all(workerPromises);\n+\n+  // Combine letter frequency results\n+  const combined = {};\n+  for (const freq of results) {\n+    for (const letter in freq) {\n+      combined[letter] = (combined[letter] || 0) + freq[letter];\n+    }\n+  }\n+\n+  return combined;\n };\n", "proposed_model_patches": [], "eval_result": "unresolved", "success": true}