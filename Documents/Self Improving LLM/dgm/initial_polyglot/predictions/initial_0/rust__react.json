{"instance_id": "rust__react", "model_name_or_path": "20250510_021328--claude-3-5-sonnet-20241022_0", "model_patch": "diff --git a/src/lib.rs b/src/lib.rs\nindex 0885ae2..ea3bddf 100644\n--- a/src/lib.rs\n+++ b/src/lib.rs\n@@ -1,6 +1,7 @@\n /// `InputCellId` is a unique identifier for an input cell.\n #[derive(<PERSON>lone, Copy, Debug, PartialEq, Eq)]\n-pub struct InputCellId();\n+pub struct InputCellId(pub usize);\n+\n /// `ComputeCellId` is a unique identifier for a compute cell.\n /// Values of type `InputCellId` and `ComputeCellId` should not be mutually assignable,\n /// demonstrated by the following tests:\n@@ -16,9 +17,10 @@ pub struct InputCellId();\n /// let compute: react::InputCellId = r.create_compute(&[react::CellId::Input(input)], |_| 222).unwrap();\n /// ```\n #[derive(<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>bu<PERSON>, <PERSON><PERSON><PERSON>q, Eq)]\n-pub struct ComputeCellId();\n+pub struct ComputeCellId(pub usize);\n+\n #[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, PartialEq, Eq)]\n-pub struct CallbackId();\n+pub struct CallbackId(pub usize);\n \n #[derive(Clone, Copy, Debug, PartialEq, Eq)]\n pub enum CellId {\n@@ -32,21 +34,42 @@ pub enum RemoveCallbackError {\n     NonexistentCallback,\n }\n \n-pub struct Reactor<T> {\n-    // Just so that the compiler doesn't complain about an unused type parameter.\n-    // You probably want to delete this field.\n-    dummy: ::std::marker::PhantomData<T>,\n+// Internal representation of a compute cell.\n+struct ComputeCell<T: Copy + PartialEq> {\n+    value: T,\n+    dependencies: Vec<CellId>,\n+    compute_func: Box<dyn Fn(&[T]) -> T>,\n+    callbacks: Vec<(CallbackId, Box<dyn FnMut(T)>)>,\n+}\n+\n+pub struct Reactor<T: Copy + PartialEq> {\n+    input_cells: Vec<T>,\n+    compute_cells: Vec<ComputeCell<T>>,\n+    next_callback_id: usize,\n }\n \n-// You are guaranteed that Reactor will only be tested against types that are Copy + PartialEq.\n impl<T: Copy + PartialEq> Reactor<T> {\n     pub fn new() -> Self {\n-        todo!()\n+        Reactor {\n+            input_cells: Vec::new(),\n+            compute_cells: Vec::new(),\n+            next_callback_id: 0,\n+        }\n+    }\n+\n+    // Helper function: get the current value of a cell by CellId.\n+    fn get_cell_value(&self, id: CellId) -> Option<T> {\n+        match id {\n+            CellId::Input(InputCellId(i)) => self.input_cells.get(i).copied(),\n+            CellId::Compute(ComputeCellId(i)) => self.compute_cells.get(i).map(|cell| cell.value),\n+        }\n     }\n \n     // Creates an input cell with the specified initial value, returning its ID.\n-    pub fn create_input(&mut self, _initial: T) -> InputCellId {\n-        todo!()\n+    pub fn create_input(&mut self, initial: T) -> InputCellId {\n+        let id = InputCellId(self.input_cells.len());\n+        self.input_cells.push(initial);\n+        id\n     }\n \n     // Creates a compute cell with the specified dependencies and compute function.\n@@ -62,12 +85,30 @@ impl<T: Copy + PartialEq> Reactor<T> {\n     // Notice that there is no way to *remove* a cell.\n     // This means that you may assume, without checking, that if the dependencies exist at creation\n     // time they will continue to exist as long as the Reactor exists.\n-    pub fn create_compute<F: Fn(&[T]) -> T>(\n+    pub fn create_compute<F: 'static + Fn(&[T]) -> T>(\n         &mut self,\n-        _dependencies: &[CellId],\n-        _compute_func: F,\n+        dependencies: &[CellId],\n+        compute_func: F,\n     ) -> Result<ComputeCellId, CellId> {\n-        todo!()\n+        // Verify dependencies\n+        let mut dep_values = Vec::with_capacity(dependencies.len());\n+        for dep in dependencies {\n+            if let Some(val) = self.get_cell_value(*dep) {\n+                dep_values.push(val);\n+            } else {\n+                return Err(*dep);\n+            }\n+        }\n+        let new_value = compute_func(&dep_values);\n+        let cell = ComputeCell {\n+            value: new_value,\n+            dependencies: dependencies.to_vec(),\n+            compute_func: Box::new(compute_func),\n+            callbacks: Vec::new(),\n+        };\n+        let id = ComputeCellId(self.compute_cells.len());\n+        self.compute_cells.push(cell);\n+        Ok(id)\n     }\n \n     // Retrieves the current value of the cell, or None if the cell does not exist.\n@@ -78,7 +119,7 @@ impl<T: Copy + PartialEq> Reactor<T> {\n     // It turns out this introduces a significant amount of extra complexity to this exercise.\n     // We chose not to cover this here, since this exercise is probably enough work as-is.\n     pub fn value(&self, id: CellId) -> Option<T> {\n-        todo!(\"Get the value of the cell whose id is {id:?}\")\n+        self.get_cell_value(id)\n     }\n \n     // Sets the value of the specified input cell.\n@@ -89,8 +130,47 @@ impl<T: Copy + PartialEq> Reactor<T> {\n     // a `set_value(&mut self, new_value: T)` method on `Cell`.\n     //\n     // As before, that turned out to add too much extra complexity.\n-    pub fn set_value(&mut self, _id: InputCellId, _new_value: T) -> bool {\n-        todo!()\n+    pub fn set_value(&mut self, id: InputCellId, new_value: T) -> bool {\n+        if id.0 >= self.input_cells.len() {\n+            return false;\n+        }\n+\n+        // Record the previous stable values of all compute cells.\n+        let prev_values: Vec<T> = self.compute_cells.iter().map(|cell| cell.value).collect();\n+\n+        // Set the input cell's value.\n+        self.input_cells[id.0] = new_value;\n+\n+        // Propagate changes until a stable state is reached.\n+        let mut changed = true;\n+        while changed {\n+            changed = false;\n+            for cell in &mut self.compute_cells {\n+                // Gather dependency values in order.\n+                let deps: Option<Vec<T>> = cell.dependencies.iter()\n+                    .map(|dep| self.get_cell_value(*dep))\n+                    .collect();\n+                if let Some(dep_values) = deps {\n+                    let new_val = (cell.compute_func)(&dep_values);\n+                    if new_val != cell.value {\n+                        cell.value = new_val;\n+                        changed = true;\n+                    }\n+                }\n+            }\n+        }\n+\n+        // After stable state, invoke callbacks for compute cells whose value changed from the previous state.\n+        for (i, cell) in self.compute_cells.iter_mut().enumerate() {\n+            if cell.value != prev_values[i] {\n+                // Call each callback exactly once with the final value.\n+                for (_cb_id, cb) in cell.callbacks.iter_mut() {\n+                    cb(cell.value);\n+                }\n+            }\n+        }\n+\n+        true\n     }\n \n     // Adds a callback to the specified compute cell.\n@@ -105,12 +185,19 @@ impl<T: Copy + PartialEq> Reactor<T> {\n     // * Exactly once if the compute cell's value changed as a result of the set_value call.\n     //   The value passed to the callback should be the final value of the compute cell after the\n     //   set_value call.\n-    pub fn add_callback<F: FnMut(T)>(\n+    pub fn add_callback<F: 'static + FnMut(T)>(\n         &mut self,\n-        _id: ComputeCellId,\n-        _callback: F,\n+        id: ComputeCellId,\n+        callback: F,\n     ) -> Option<CallbackId> {\n-        todo!()\n+        if let Some(cell) = self.compute_cells.get_mut(id.0) {\n+            let cb_id = CallbackId(self.next_callback_id);\n+            self.next_callback_id += 1;\n+            cell.callbacks.push((cb_id, Box::new(callback)));\n+            Some(cb_id)\n+        } else {\n+            None\n+        }\n     }\n \n     // Removes the specified callback, using an ID returned from add_callback.\n@@ -120,11 +207,19 @@ impl<T: Copy + PartialEq> Reactor<T> {\n     // A removed callback should no longer be called.\n     pub fn remove_callback(\n         &mut self,\n-        cell: ComputeCellId,\n-        callback: CallbackId,\n+        cell_id: ComputeCellId,\n+        callback_id: CallbackId,\n     ) -> Result<(), RemoveCallbackError> {\n-        todo!(\n-            \"Remove the callback identified by the CallbackId {callback:?} from the cell {cell:?}\"\n-        )\n+        if let Some(cell) = self.compute_cells.get_mut(cell_id.0) {\n+            let original_len = cell.callbacks.len();\n+            cell.callbacks.retain(|(id, _)| *id != callback_id);\n+            if cell.callbacks.len() == original_len {\n+                Err(RemoveCallbackError::NonexistentCallback)\n+            } else {\n+                Ok(())\n+            }\n+        } else {\n+            Err(RemoveCallbackError::NonexistentCell)\n+        }\n     }\n }\n", "proposed_model_patches": [], "eval_result": "unresolved", "success": true}