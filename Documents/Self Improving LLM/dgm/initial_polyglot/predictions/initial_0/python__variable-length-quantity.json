{"instance_id": "python__variable-length-quantity", "model_name_or_path": "20250510_021328--claude-3-5-sonnet-20241022_0", "model_patch": "diff --git a/variable_length_quantity.py b/variable_length_quantity.py\nindex 41bdcdc..ac1888c 100644\n--- a/variable_length_quantity.py\n+++ b/variable_length_quantity.py\n@@ -1,6 +1,64 @@\n def encode(numbers):\n-    pass\n+    \"\"\"\n+    Encode a list of integers into a stream of bytes using Variable Length Quantity encoding.\n+    Each integer is encoded into one or more bytes. In each byte, only the lower 7 bits are used to store data.\n+    All bytes except the last have the most significant bit (MSB) set to 1 to indicate that more bytes follow.\n+    \n+    For example:\n+      - 0 is encoded as [0x00]\n+      - 127 (0x7F) is encoded as [0x7F]\n+      - 128 (0x80) is encoded as [0x81, 0x00]\n+    \n+    :param numbers: A list of integers (each expected to be in the range 0 to 0xFFFFFFFF).\n+    :return: A list of encoded bytes.\n+    \"\"\"\n+    result = []\n+    for number in numbers:\n+        if number == 0:\n+            result.append(0)\n+            continue\n+        current_bytes = []\n+        while number > 0:\n+            # Take the lowest 7 bits\n+            current_bytes.append(number & 0x7F)\n+            number >>= 7\n+        # Now, current_bytes has the 7-bit parts in little endian order.\n+        # When encoding, we need to output them in big-endian order, and mark all but the last with the MSB set.\n+        for i in range(len(current_bytes)-1, -1, -1):\n+            byte = current_bytes[i]\n+            # If not the last byte (i.e., not the last in order), set the continuation flag\n+            if i != 0:\n+                byte |= 0x80\n+            result.append(byte)\n+    return result\n \n \n def decode(bytes_):\n-    pass\n+    \"\"\"\n+    Decode a stream of bytes encoded in VLQ format into a list of integers.\n+    Each integer is encoded in one or more bytes. If a byte has the MSB set, it indicates that the following byte is part\n+    of the same number. The lower 7 bits of each byte are concatenated in big-endian order to form the integer.\n+    \n+    Raises a ValueError with a meaningful message if a sequence is incomplete.\n+    \n+    :param bytes_: A list of integers representing the bytes of the encoded stream.\n+    :return: A list of decoded integers.\n+    :raises ValueError: if the byte sequence ends before a complete number is decoded.\n+    \"\"\"\n+    result = []\n+    current = 0\n+    in_progress = False\n+    for byte in bytes_:\n+        # Accumulate the 7-bit group by shifting left 7 bits and adding the lower 7 bits\n+        current = (current << 7) | (byte & 0x7F)\n+        # If the MSB is not set, then it is the last byte in this number\n+        if (byte & 0x80) == 0:\n+            result.append(current)\n+            current = 0\n+            in_progress = False\n+        else:\n+            in_progress = True\n+    # If we finished processing but are still in the middle of a sequence, it's an error\n+    if in_progress:\n+        raise ValueError(\"incomplete sequence\")\n+    return result\n", "proposed_model_patches": [], "eval_result": "resolved", "success": true}