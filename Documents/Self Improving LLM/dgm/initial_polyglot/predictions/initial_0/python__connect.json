{"instance_id": "python__connect", "model_name_or_path": "20250510_021328--claude-3-5-sonnet-20241022_0", "model_patch": "diff --git a/connect.py b/connect.py\nindex 688dd68..f24d0ea 100644\n--- a/connect.py\n+++ b/connect.py\n@@ -1,7 +1,51 @@\n-\n class ConnectGame:\n     def __init__(self, board):\n-        pass\n+        # board is expected to be a list of strings representing rows.\n+        # Each row may have extra whitespace for alignment; we split by whitespace to get tokens.\n+        self.raw_board = board\n+        self.board = [line.strip().split() for line in board if line.strip() != \"\"]\n+        self.n = len(self.board)\n+        self.m = len(self.board[0]) if self.n > 0 else 0\n \n     def get_winner(self):\n-        pass\n+        # Define neighbor offsets for a hex grid (using axial coordinates mapping):\n+        # for a cell (r, c) the neighbors are:\n+        # (r-1, c), (r-1, c+1), (r, c-1), (r, c+1), (r+1, c-1), (r+1, c)\n+        offsets = [(-1, 0), (-1, 1), (0, -1), (0, 1), (1, -1), (1, 0)]\n+\n+        def bfs(symbol, start_positions, target_check):\n+            from collections import deque\n+            visited = [[False] * self.m for _ in range(self.n)]\n+            queue = deque(start_positions)\n+            for r, c in start_positions:\n+                visited[r][c] = True\n+            while queue:\n+                r, c = queue.popleft()\n+                # Check if this cell meets the target condition\n+                if target_check(r, c):\n+                    return True\n+                for dr, dc in offsets:\n+                    nr, nc = r + dr, c + dc\n+                    if 0 <= nr < self.n and 0 <= nc < self.m:\n+                        if not visited[nr][nc] and self.board[nr][nc] == symbol:\n+                            visited[nr][nc] = True\n+                            queue.append((nr, nc))\n+            return False\n+\n+        # Player O plays from top to bottom. Start with top row cells marked 'O'.\n+        o_starts = [(0, c) for c in range(self.m) if self.board[0][c] == 'O']\n+        o_win = bfs('O', o_starts, lambda r, c: r == self.n - 1)\n+\n+        # Player X plays from left to right. Start with left column cells marked 'X'.\n+        x_starts = [(r, 0) for r in range(self.n) if self.board[r][0] == 'X']\n+        x_win = bfs('X', x_starts, lambda r, c: c == self.m - 1)\n+\n+        if o_win and not x_win:\n+            return \"O\"\n+        elif x_win and not o_win:\n+            return \"X\"\n+        elif o_win and x_win:\n+            # In a fair game both cannot win. If they do, we return both.\n+            return \"Both\"\n+        else:\n+            return None\n", "proposed_model_patches": [], "eval_result": "unresolved", "success": true}