{"instance_id": "python__rest-api", "model_name_or_path": "20250510_021328--claude-3-5-sonnet-20241022_0", "model_patch": "diff --git a/rest_api.py b/rest_api.py\nindex 0e448a4..50fe9a8 100644\n--- a/rest_api.py\n+++ b/rest_api.py\n@@ -1,9 +1,109 @@\n class RestAPI:\n     def __init__(self, database=None):\n-        pass\n+        # Initialize the database with a list of user objects if not provided\n+        if database is None:\n+            self.database = {\"users\": []}\n+        else:\n+            self.database = database\n+\n+    def find_user(self, name):\n+        for user in self.database[\"users\"]:\n+            if user[\"name\"] == name:\n+                return user\n+        return None\n+\n+    def update_balance(self, user):\n+        # Calculate balance as (total owed_by) - (total owes)\n+        owed_by_total = sum(user.get(\"owed_by\", {}).values())\n+        owes_total = sum(user.get(\"owes\", {}).values())\n+        user[\"balance\"] = round(owed_by_total - owes_total, 2)\n+\n+    def sort_users(self, users):\n+        # Return users sorted by name\n+        return sorted(users, key=lambda x: x[\"name\"])\n \n     def get(self, url, payload=None):\n-        pass\n+        if url == \"/users\":\n+            # If payload specifies which users to return\n+            if payload and \"users\" in payload:\n+                names = payload[\"users\"]\n+                users = [user for user in self.database[\"users\"] if user[\"name\"] in names]\n+            else:\n+                users = self.database[\"users\"]\n+            # before returning, update balance for all users\n+            for user in users:\n+                self.update_balance(user)\n+            return {\"users\": self.sort_users(users)}\n+        \n+        # For unknown GET endpoints, simply return None or empty dict\n+        return {}\n \n     def post(self, url, payload=None):\n-        pass\n+        if url == \"/add\":\n+            # Create a new user if not exists\n+            user_name = payload[\"user\"]\n+            if self.find_user(user_name) is None:\n+                new_user = {\n+                    \"name\": user_name,\n+                    \"owes\": {},\n+                    \"owed_by\": {},\n+                    \"balance\": 0.0\n+                }\n+                self.database[\"users\"].append(new_user)\n+                return new_user\n+            else:\n+                # If user exists, return the existing user\n+                return self.find_user(user_name)\n+\n+        elif url == \"/iou\":\n+            lender_name = payload[\"lender\"]\n+            borrower_name = payload[\"borrower\"]\n+            amount = payload[\"amount\"]\n+            \n+            lender = self.find_user(lender_name)\n+            borrower = self.find_user(borrower_name)\n+            \n+            if not lender or not borrower:\n+                # In a real API, we might raise an error\n+                return {}\n+            \n+            # Process netting of IOU between lender and borrower\n+            # Check if lender owes borrower already\n+            if borrower_name in lender.get(\"owes\", {}):\n+                existing = lender[\"owes\"][borrower_name]\n+                if amount >= existing:\n+                    amount -= existing\n+                    del lender[\"owes\"][borrower_name]\n+                    del borrower[\"owed_by\"][lender_name]\n+                else:\n+                    lender[\"owes\"][borrower_name] = round(existing - amount, 2)\n+                    borrower[\"owed_by\"][lender_name] = round(existing - amount, 2)\n+                    amount = 0\n+            # Else check if borrower owes lender (normal IOU direction)\n+            elif lender_name in borrower.get(\"owes\", {}):\n+                existing = borrower[\"owes\"][lender_name]\n+                if amount >= existing:\n+                    amount -= existing\n+                    del borrower[\"owes\"][lender_name]\n+                    del lender[\"owed_by\"][borrower_name]\n+                else:\n+                    borrower[\"owes\"][lender_name] = round(existing - amount, 2)\n+                    lender[\"owed_by\"][borrower_name] = round(existing - amount, 2)\n+                    amount = 0\n+            \n+            # If after netting, there is remaining amount, add it as a new IOU from borrower to lender\n+            if amount > 0:\n+                # Update lender's owed_by\n+                lender[\"owed_by\"][borrower_name] = round(lender.get(\"owed_by\", {}).get(borrower_name, 0) + amount, 2)\n+                # Update borrower's owes\n+                borrower[\"owes\"][lender_name] = round(borrower.get(\"owes\", {}).get(lender_name, 0) + amount, 2)\n+            \n+            # Update balances\n+            self.update_balance(lender)\n+            self.update_balance(borrower)\n+            \n+            # Return the updated records for lender and borrower sorted by name\n+            return {\"users\": self.sort_users([lender, borrower])}\n+\n+        # For unknown POST endpoints, simply return empty dict\n+        return {}\n", "proposed_model_patches": [], "eval_result": "unresolved", "success": true}