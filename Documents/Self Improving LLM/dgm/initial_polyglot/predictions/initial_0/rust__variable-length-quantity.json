{"instance_id": "rust__variable-length-quantity", "model_name_or_path": "20250510_021328--claude-3-5-sonnet-20241022_0", "model_patch": "diff --git a/src/lib.rs b/src/lib.rs\nindex 15f55c6..df5da44 100644\n--- a/src/lib.rs\n+++ b/src/lib.rs\n@@ -1,3 +1,5 @@\n+/// Implements variable length quantity encoding and decoding.\n+\n #[derive(Debug, PartialEq, Eq)]\n pub enum Error {\n     IncompleteNumber,\n@@ -5,10 +7,58 @@ pub enum Error {\n \n /// Convert a list of numbers to a stream of bytes encoded with variable length encoding.\n pub fn to_bytes(values: &[u32]) -> Vec<u8> {\n-    todo!(\"Convert the values {values:?} to a list of bytes\")\n+    let mut result = Vec::new();\n+\n+    for &value in values {\n+        // Build the VLQ encoding for each value\n+        let mut buffer = Vec::new();\n+        let mut x = value;\n+        // At least one iteration is needed to encode 0 properly\n+        loop {\n+            // Take the lowest 7 bits\n+            buffer.push((x & 0x7F) as u8);\n+            // If there are no more bits left, break\n+            if x < 128 {\n+                break;\n+            }\n+            x >>= 7;\n+        }\n+        // The bytes in buffer are in little-endian order (least significant group first), so reverse them\n+        buffer.reverse();\n+        // Set the continuation bits for all but the last byte\n+        for i in 0..buffer.len()-1 {\n+            buffer[i] |= 0x80;\n+        }\n+        result.extend(buffer);\n+    }\n+\n+    result\n }\n \n /// Given a stream of bytes, extract all numbers which are encoded in there.\n pub fn from_bytes(bytes: &[u8]) -> Result<Vec<u32>, Error> {\n-    todo!(\"Convert the list of bytes {bytes:?} to a list of numbers\")\n+    let mut result = Vec::new();\n+    let mut value: u32 = 0;\n+    // This flag tracks if we are in the middle of reading a number\n+    let mut in_number = false;\n+\n+    for &byte in bytes {\n+        in_number = true;\n+        // Shift current value 7 bits to the left and add the new 7 bits\n+        value = (value << 7) | (byte & 0x7F) as u32;\n+        \n+        // If the continuation bit is not set, this is the end of the current number\n+        if (byte & 0x80) == 0 {\n+            result.push(value);\n+            value = 0;\n+            in_number = false;\n+        }\n+    }\n+    \n+    // If the last byte had the continuation bit set, then the final number is incomplete\n+    if in_number {\n+        return Err(Error::IncompleteNumber);\n+    }\n+    \n+    Ok(result)\n }\n", "proposed_model_patches": [], "eval_result": "resolved", "success": true}