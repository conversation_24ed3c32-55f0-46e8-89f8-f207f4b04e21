{"instance_id": "javascript__affine-cipher", "model_name_or_path": "20250510_021328--claude-3-5-sonnet-20241022_0", "model_patch": "diff --git a/affine-cipher.js b/affine-cipher.js\nindex 545736f..312b376 100644\n--- a/affine-cipher.js\n+++ b/affine-cipher.js\n@@ -1,7 +1,92 @@\n+const ALPHABET_SIZE = 26;\n+\n+// Helper function to compute the Greatest Common Divisor using <PERSON><PERSON><PERSON>'s algorithm\n+const gcd = (a, b) => b === 0 ? a : gcd(b, a % b);\n+\n+// Helper function to compute modular inverse of a under modulo m\n+// Returns the inverse if exists, otherwise returns null\n+const modInverse = (a, m) => {\n+  // Brute force approach: find x such that (a * x) % m === 1\n+  a = ((a % m) + m) % m; // normalize a\n+  for (let x = 1; x < m; x++) {\n+    if ((a * x) % m === 1) {\n+      return x;\n+    }\n+  }\n+  return null; // No modular inverse if we'll never find one\n+};\n+\n+// Remove all non-alphanumeric characters, but keep digits intact. Convert letters to lowercase.\n+const sanitize = (text) => {\n+  return text.toLowerCase().split('').filter(ch => /[a-z0-9]/.test(ch)).join('');\n+};\n+\n+// Group text into chunks of size n separated by a space\n+const groupText = (text, n=5) => {\n+  let grouped = '';\n+  for (let i = 0; i < text.length; i++) {\n+    if (i > 0 && i % n === 0) {\n+      grouped += ' ';\n+    }\n+    grouped += text[i];\n+  }\n+  return grouped;\n+};\n+\n export const encode = (phrase, key) => {\n-  throw new Error('Remove this statement and implement this function');\n+  // key should be an object with keys a and b\n+  const { a, b } = key;\n+  if (gcd(a, ALPHABET_SIZE) !== 1) {\n+    throw new Error('Error: a and 26 are not coprime');\n+  }\n+\n+  const sanitized = sanitize(phrase);\n+  let encoded = '';\n+\n+  for (let ch of sanitized) {\n+    if (/[a-z]/.test(ch)) {\n+      // Convert letter to 0-index (a=0, b=1, ...)\n+      const index = ch.charCodeAt(0) - 'a'.charCodeAt(0);\n+      const transformed = (a * index + b) % ALPHABET_SIZE;\n+      // Convert back to letter and append\n+      encoded += String.fromCharCode('a'.charCodeAt(0) + transformed);\n+    } else if (/[0-9]/.test(ch)) {\n+      // Digits remain as is\n+      encoded += ch;\n+    }\n+  }\n+\n+  return groupText(encoded);\n };\n \n export const decode = (phrase, key) => {\n-  throw new Error('Remove this statement and implement this function');\n+  const { a, b } = key;\n+  if (gcd(a, ALPHABET_SIZE) !== 1) {\n+    throw new Error('Error: a and 26 are not coprime');\n+  }\n+  \n+  // Get modular multiplicative inverse of a modulo ALPHABET_SIZE\n+  const aInv = modInverse(a, ALPHABET_SIZE);\n+  if (aInv === null) {\n+    throw new Error('Error: modular inverse does not exist');\n+  }\n+\n+  // Remove spaces and non-alphanumerics (we only expect letters and digits now)\n+  const sanitized = sanitize(phrase);\n+  let decoded = '';\n+\n+  for (let ch of sanitized) {\n+    if (/[a-z]/.test(ch)) {\n+      const index = ch.charCodeAt(0) - 'a'.charCodeAt(0);\n+      // Compute D(y) = a_inv*(y - b) mod 26 \n+      let decodedIndex = aInv * (index - b);\n+      // Use modulo operation that handles negative numbers properly\n+      decodedIndex = ((decodedIndex % ALPHABET_SIZE) + ALPHABET_SIZE) % ALPHABET_SIZE;\n+      decoded += String.fromCharCode('a'.charCodeAt(0) + decodedIndex);\n+    } else if (/[0-9]/.test(ch)) {\n+      decoded += ch;\n+    }\n+  }\n+\n+  return decoded;\n };\n", "proposed_model_patches": [], "eval_result": "unresolved", "success": true}