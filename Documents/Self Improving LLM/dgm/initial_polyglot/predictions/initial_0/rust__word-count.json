{"instance_id": "rust__word-count", "model_name_or_path": "20250510_021328--claude-3-5-sonnet-20241022_0", "model_patch": "diff --git a/src/lib.rs b/src/lib.rs\nindex a0e9034..3d52084 100644\n--- a/src/lib.rs\n+++ b/src/lib.rs\n@@ -1,6 +1,40 @@\n use std::collections::HashMap;\n \n-/// Count occurrences of words.\n-pub fn word_count(words: &str) -> HashMap<String, u32> {\n-    todo!(\"Count of occurrences of words in {words:?}\");\n+/// Count occurrences of words in a given subtitle text.\n+/// \n+/// The function treats words in a case-insensitive manner and counts contractions as single words.\n+/// Words are defined as sequences of alphanumeric characters possibly containing internal apostrophes.\n+/// This means that punctuation (other than apostrophes within a word) and whitespace act as delimiters.\n+/// \n+/// Example:\n+/// Input: \"That's the password: 'PASSWORD 123'!\", cried the Special Agent.\\nSo I fled.\n+/// Output mapping (order does not matter):\n+/// 123: 1\n+/// agent: 1\n+/// cried: 1\n+/// fled: 1\n+/// i: 1\n+/// password: 2\n+/// so: 1\n+/// special: 1\n+/// that's: 1\n+/// the: 2\n+pub fn word_count(text: &str) -> HashMap<String, u32> {\n+    // This regex matches a word as a sequence of alphanumeric characters\n+    // with internal apostrophes allowed (e.g., contractions like \"they're\").\n+    // The pattern breakdown:\n+    // [A-Za-z0-9]+ : one or more letters or digits\n+    // (?:'[A-Za-z0-9]+)* : zero or more groups of an apostrophe followed by one or more letters/digits\n+    let re = regex::Regex::new(r\"[A-Za-z0-9]+(?:'[A-Za-z0-9]+)*\").unwrap();\n+\n+    let mut counts = HashMap::new();\n+    \n+    // Iterate over all regex matches in the input text\n+    for mat in re.find_iter(text) {\n+        // Convert the word to lowercase for case-insensitive comparison\n+        let word = mat.as_str().to_lowercase();\n+        *counts.entry(word).or_insert(0) += 1;\n+    }\n+\n+    counts\n }\n", "proposed_model_patches": [], "eval_result": "unresolved", "success": true}