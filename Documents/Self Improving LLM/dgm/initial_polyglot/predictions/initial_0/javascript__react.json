{"instance_id": "javascript__react", "model_name_or_path": "20250510_021328--claude-3-5-sonnet-20241022_0", "model_patch": "diff --git a/react.js b/react.js\nindex dfbd975..b2b02be 100644\n--- a/react.js\n+++ b/react.js\n@@ -5,30 +5,96 @@\n \n export class InputCell {\n   constructor(value) {\n-    throw new Error('Remove this statement and implement this function');\n+    this.value = value;\n+    // track dependent compute cells\n+    this.dependents = [];\n   }\n \n   setValue(value) {\n-    throw new Error('Remove this statement and implement this function');\n+    if (this.value === value) {\n+      return;\n+    }\n+    this.value = value;\n+    // propagate updates to all dependent compute cells\n+    this.propagate();\n+  }\n+\n+  propagate() {\n+    // Using a set to avoid duplicate updates\n+    const visited = new Set();\n+    const updateQueue = [];\n+    \n+    // initialize with direct dependents\n+    for (const dependent of this.dependents) {\n+      updateQueue.push(dependent);\n+      visited.add(dependent);\n+    }\n+\n+    while (updateQueue.length > 0) {\n+      const cell = updateQueue.shift();\n+      // compute new value based on current input values\n+      const newVal = cell.compute();\n+      if (newVal !== cell.value) {\n+        cell.value = newVal;\n+        // if value changed, call its callbacks\n+        cell.fireCallbacks(newVal);\n+        // then add its dependents to queue\n+        for (const dep of cell.dependents) {\n+          if (!visited.has(dep)) {\n+            visited.add(dep);\n+            updateQueue.push(dep);\n+          }\n+        }\n+      }\n+    }\n   }\n }\n \n export class ComputeCell {\n   constructor(inputCells, fn) {\n-    throw new Error('Remove this statement and implement this function');\n+    this.inputCells = inputCells;\n+    this.fn = fn;\n+    this.callbacks = [];\n+    // track dependent compute cells\n+    this.dependents = [];\n+    // initial value computed from input cells\n+    this.value = this.compute();\n+\n+    // register self as a dependent to each input cell\n+    for (const cell of inputCells) {\n+      // input cell could be an InputCell or another ComputeCell\n+      if (!cell.dependents) {\n+        cell.dependents = [];\n+      }\n+      cell.dependents.push(this);\n+    }\n+  }\n+\n+  // compute new value by applying the function over the current values of input cells\n+  compute() {\n+    const values = this.inputCells.map(cell => cell.value);\n+    return this.fn(...values);\n+  }\n+\n+  fireCallbacks(newVal) {\n+    for (const callbackCell of this.callbacks) {\n+      callbackCell.fn(newVal);\n+    }\n   }\n \n   addCallback(cb) {\n-    throw new Error('Remove this statement and implement this function');\n+    const callbackCell = new CallbackCell(cb);\n+    this.callbacks.push(callbackCell);\n+    return callbackCell;\n   }\n \n-  removeCallback(cb) {\n-    throw new Error('Remove this statement and implement this function');\n+  removeCallback(callbackCell) {\n+    this.callbacks = this.callbacks.filter(cell => cell !== callbackCell);\n   }\n }\n \n export class CallbackCell {\n   constructor(fn) {\n-    throw new Error('Remove this statement and implement this function');\n+    this.fn = fn;\n   }\n }\n", "proposed_model_patches": [], "eval_result": "unresolved", "success": true}