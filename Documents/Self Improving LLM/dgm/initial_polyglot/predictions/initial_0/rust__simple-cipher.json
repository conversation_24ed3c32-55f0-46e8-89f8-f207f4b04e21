{"instance_id": "rust__simple-cipher", "model_name_or_path": "20250510_021328--claude-3-5-sonnet-20241022_0", "model_patch": "diff --git a/src/lib.rs b/src/lib.rs\nindex b3ab8b1..5040f74 100644\n--- a/src/lib.rs\n+++ b/src/lib.rs\n@@ -1,11 +1,70 @@\n+use rand::Rng;\n+\n+// This module implements a simple substitution cipher that can work as a Caesar Cipher when the key is uniform.\n+// The key must consist solely of lowercase alphabet characters (a-z).\n+// For encoding, each character of the plaintext is shifted by a number determined by the corresponding character in the key (key repeats if necessary).\n+// For decoding, the reverse shift is applied.\n+\n+// Returns Some(encoded_string) if key is valid (non-empty and only lowercase letters), otherwise returns None.\n+\n pub fn encode(key: &str, s: &str) -> Option<String> {\n-    todo!(\"Use {key} to encode {s} using shift cipher\")\n+    if key.is_empty() || !key.chars().all(|c| c.is_ascii_lowercase()) {\n+        return None;\n+    }\n+    // Cycle through the key if it's shorter than the plaintext.\n+    let encoded: String = s.chars()\n+        .zip(key.chars().cycle())\n+        .map(|(c, key_c)| {\n+            if c.is_ascii_lowercase() {\n+                let shift = key_c as u8 - b'a';\n+                let base = b'a';\n+                // Perform shift using mod 26 arithmetic.\n+                let new_val = (c as u8 - base + shift) % 26 + base;\n+                new_val as char\n+            } else {\n+                // For non-lowercase letters, return them unchanged.\n+                c\n+            }\n+        })\n+        .collect();\n+    Some(encoded)\n }\n \n+// Returns Some(decoded_string) if key is valid (non-empty and only lowercase letters), otherwise returns None.\n+\n pub fn decode(key: &str, s: &str) -> Option<String> {\n-    todo!(\"Use {key} to decode {s} using shift cipher\")\n+    if key.is_empty() || !key.chars().all(|c| c.is_ascii_lowercase()) {\n+        return None;\n+    }\n+    let decoded: String = s.chars()\n+        .zip(key.chars().cycle())\n+        .map(|(c, key_c)| {\n+            if c.is_ascii_lowercase() {\n+                let shift = key_c as u8 - b'a';\n+                let base = b'a';\n+                // To decode, subtract the shift and add 26 to avoid negative values.\n+                let new_val = (26 + (c as u8 - base) - shift) % 26 + base;\n+                new_val as char\n+            } else {\n+                c\n+            }\n+        })\n+        .collect();\n+    Some(decoded)\n }\n \n+// Generates a random key composed solely of lowercase letters (a-z) of length at least 100.\n+// Encodes the string s using this generated key and returns the tuple (key, encoded_string).\n+\n pub fn encode_random(s: &str) -> (String, String) {\n-    todo!(\"Generate random key with only a-z chars and encode {s}. Return tuple (key, encoded s)\")\n+    let key_len = 100;\n+    let mut rng = rand::thread_rng();\n+    let key: String = (0..key_len)\n+        .map(|_| {\n+            let r = rng.gen_range(0..26);\n+            (b'a' + r as u8) as char\n+        })\n+        .collect();\n+    let encoded = encode(&key, s).unwrap();\n+    (key, encoded)\n }\n", "proposed_model_patches": [], "eval_result": "resolved", "success": true}