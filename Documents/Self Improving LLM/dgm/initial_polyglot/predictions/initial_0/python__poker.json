{"instance_id": "python__poker", "model_name_or_path": "20250510_021328--claude-3-5-sonnet-20241022_0", "model_patch": "diff --git a/poker.py b/poker.py\nindex 49824d3..1fa7eb4 100644\n--- a/poker.py\n+++ b/poker.py\n@@ -1,2 +1,161 @@\n+from collections import Counter\n+\n+# Map card characters to their values\n+CARD_VALUES = {\n+    '2': 2,\n+    '3': 3,\n+    '4': 4,\n+    '5': 5,\n+    '6': 6,\n+    '7': 7,\n+    '8': 8,\n+    '9': 9,\n+    'T': 10,\n+    'J': 11,\n+    'Q': 12,\n+    'K': 13,\n+    'A': 14,\n+}\n+\n+\n+def parse_hand(hand_str):\n+    \"\"\"\n+    Parses a hand string.\n+    The hand string is expected to be in the format \"Name: CARD CARD CARD CARD CARD\"\n+    If no colon is present, treat the entire string as cards (without a name).\n+    Returns a tuple (name, cards) where name may be None if not provided.\n+    Each card is a tuple (value, suit) with value as int.\n+    \"\"\"\n+    if ':' in hand_str:\n+        name, cards_part = hand_str.split(':', 1)\n+        name = name.strip()\n+        card_tokens = cards_part.strip().split()\n+    else:\n+        name = None\n+        card_tokens = hand_str.split()\n+    \n+    cards = []\n+    for token in card_tokens:\n+        # card format e.g. \"2H\", \"TD\", etc.\n+        value_char = token[0]\n+        suit = token[1]\n+        value = CARD_VALUES[value_char]\n+        cards.append((value, suit))\n+    return name, cards\n+\n+\n+\n+def is_flush(cards):\n+    suits = [s for (v, s) in cards]\n+    return len(set(suits)) == 1\n+\n+\n+def is_straight(values):\n+    \"\"\"\n+    Check if the sorted list of values forms a straight. Handles Ace-low straight as well.\n+    \"\"\"\n+    sorted_vals = sorted(values)\n+    # Check normal straight\n+    for i in range(4):\n+        if sorted_vals[i+1] - sorted_vals[i] != 1:\n+            break\n+    else:\n+        return True, max(sorted_vals)\n+    \n+    # Check Ace-low straight: Ace, 2, 3, 4, 5\n+    if set(sorted_vals) == {2, 3, 4, 5, 14}:\n+        return True, 5\n+    return False, None\n+\n+\n+def hand_rank(cards):\n+    \"\"\"\n+    Returns a tuple representing the rank of the hand.\n+    Higher tuples compare as better hands.\n+    The tuple is structured as (hand_type, primary, secondary, ...)\n+    where hand_type is an integer with higher numbers for better hands.\n+    Rankings:\n+      8: Straight flush\n+      7: Four of a kind\n+      6: Full house\n+      5: Flush\n+      4: Straight\n+      3: Three of a kind\n+      2: Two pair\n+      1: One pair\n+      0: High card\n+    \"\"\"\n+    values = [v for (v, s) in cards]\n+    counts = Counter(values)\n+    count_items = counts.items()  # (value, count)\n+    # sort by count descending, then value descending\n+    sorted_counts = sorted(count_items, key=lambda x: (x[1], x[0]), reverse=True)\n+    flush = is_flush(cards)\n+    straight, high_straight = is_straight(values)\n+    \n+    if flush and straight:\n+        return (8, high_straight)  # Straight flush\n+    if sorted_counts[0][1] == 4:\n+        # Four of a kind: (quad, kicker)\n+        four_val = sorted_counts[0][0]\n+        kicker = [v for v in values if v != four_val][0]\n+        return (7, four_val, kicker)\n+    if sorted_counts[0][1] == 3 and sorted_counts[1][1] == 2:\n+        # Full house: (trip, pair)\n+        return (6, sorted_counts[0][0], sorted_counts[1][0])\n+    if flush:\n+        # Flush: sort values descending\n+        return (5,) + tuple(sorted(values, reverse=True))\n+    if straight:\n+        return (4, high_straight)\n+    if sorted_counts[0][1] == 3:\n+        # Three of a kind: (trip, kickers...)\n+        trip = sorted_counts[0][0]\n+        kickers = sorted((v for v in values if v != trip), reverse=True)\n+        return (3, trip) + tuple(kickers)\n+    if sorted_counts[0][1] == 2 and sorted_counts[1][1] == 2:\n+        # Two pair: (high pair, low pair, kicker)\n+        high_pair = max(sorted_counts[0][0], sorted_counts[1][0])\n+        low_pair = min(sorted_counts[0][0], sorted_counts[1][0])\n+        kicker = [v for v in values if v != high_pair and v != low_pair][0]\n+        return (2, high_pair, low_pair, kicker)\n+    if sorted_counts[0][1] == 2:\n+        # One pair: (pair, kickers...)\n+        pair = sorted_counts[0][0]\n+        kickers = sorted((v for v in values if v != pair), reverse=True)\n+        return (1, pair) + tuple(kickers)\n+    # High card\n+    return (0,) + tuple(sorted(values, reverse=True))\n+\n+\n def best_hands(hands):\n-    pass\n+    \"\"\"\n+    Given a list of hand strings, returns the hand or list of hands that wins.\n+    If there is a tie, return all tied hands in the order they were given.\n+    \"\"\"\n+    # Prepare list of tuples: (original_hand_str, name, cards, rank)\n+    evaluated = []\n+    for hand_str in hands:\n+        name, cards = parse_hand(hand_str)\n+        rank = hand_rank(cards)\n+        evaluated.append((hand_str, name, cards, rank))\n+\n+    # Find the best rank\n+    best = max(evaluated, key=lambda x: x[3])[3]\n+    winners = [hand_str for (hand_str, name, cards, rank) in evaluated if rank == best]\n+    return winners\n+\n+\n+# Example usage when run as a script\n+if __name__ == '__main__':\n+    # Example hands; you can modify these for testing\n+    sample_hands = [\n+        \"Black: 2H 3D 5S 9C KD\",\n+        \"White: 2C 3H 4S 8C AH\",\n+        \"Red: 2H 4S 4C 2D 4H\",\n+        \"Blue: 2S 8S AS QS 3S\"\n+    ]\n+    winners = best_hands(sample_hands)\n+    print(\"Winning hand(s):\")\n+    for w in winners:\n+        print(w)\n", "proposed_model_patches": [], "eval_result": "unresolved", "success": true}