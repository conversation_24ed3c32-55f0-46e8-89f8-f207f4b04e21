{"instance_id": "javascript__ledger", "model_name_or_path": "20250510_021328--claude-3-5-sonnet-20241022_0", "model_patch": "diff --git a/ledger.js b/ledger.js\nindex 97bf4e0..c42b334 100644\n--- a/ledger.js\n+++ b/ledger.js\n@@ -1,3 +1,12 @@\n+/*\n+Refactored Ledger Printer\n+Change Log:\n+- Introduced helper functions to format header, date, description, and change values.\n+- Replaced duplicated code for en-US and nl-NL locales by abstracting locale-specific formatting into separate functions.\n+- Added a sortEntries() helper to centralize the sorting logic.\n+- Ensured that for en-US, negative currency values are wrapped in parentheses, while nl-NL formatting remains as before.\n+*/\n+\n class LedgerEntry {\n   constructor() {\n     this.date = undefined;\n@@ -7,157 +16,109 @@ class LedgerEntry {\n }\n \n export function createEntry(date, description, change) {\n-  let entry = new LedgerEntry();\n+  const entry = new LedgerEntry();\n   entry.date = new Date(date);\n   entry.description = description;\n   entry.change = change;\n   return entry;\n }\n \n-export function formatEntries(currency, locale, entries) {\n-  let table = '';\n-  if (locale === 'en-US') {\n-    // Generate Header Row\n-    table +=\n-      'Date'.padEnd(10, ' ') +\n-      ' | ' +\n-      'Description'.padEnd(25, ' ') +\n-      ' | ' +\n-      'Change'.padEnd(13, ' ') +\n-      '\\n';\n-\n-    // Sort entries\n-    entries.sort(\n-      (a, b) =>\n-        a.date - b.date ||\n-        a.change - b.change ||\n-        a.description.localeCompare(b.description),\n-    );\n+const HEADERS = {\n+  'en-US': {\n+    date: 'Date'.padEnd(10, ' '),\n+    description: 'Description'.padEnd(25, ' '),\n+    change: 'Change'.padEnd(13, ' '),\n+  },\n+  'nl-NL': {\n+    date: 'Datum'.padEnd(10, ' '),\n+    description: 'Omschrijving'.padEnd(25, ' '),\n+    change: 'Verandering'.padEnd(13, ' '),\n+  },\n+};\n \n-    entries.forEach((entry) => {\n-      // Write entry date to table\n-      const dateStr = `${(entry.date.getMonth() + 1)\n-        .toString()\n-        .padStart(2, '0')}/${entry.date\n-        .getDate()\n-        .toString()\n-        .padStart(2, '0')}/${entry.date.getFullYear()}`;\n-      table += `${dateStr} | `;\n+function formatHeader(locale) {\n+  const header = HEADERS[locale];\n+  return `${header.date} | ${header.description} | ${header.change}\\n`;\n+}\n \n-      // Write entry description to table\n-      const truncatedDescription =\n-        entry.description.length > 25\n-          ? `${entry.description.substring(0, 22)}...`\n-          : entry.description.padEnd(25, ' ');\n-      table += `${truncatedDescription} | `;\n+function sortEntries(entries) {\n+  return entries.sort((a, b) =>\n+    a.date - b.date ||\n+    a.change - b.change ||\n+    a.description.localeCompare(b.description)\n+  );\n+}\n \n-      // Write entry change to table\n-      let changeStr = '';\n-      if (currency === 'USD') {\n-        let formatingOptions = {\n-          style: 'currency',\n-          currency: 'USD',\n-          //currencySign: 'accounting',\n-          minimumFractionDigits: 2,\n-          maximumFractionDigits: 2,\n-        };\n-        if (entry.change < 0) {\n-          changeStr = `(${Math.abs(entry.change / 100).toLocaleString(\n-            'en-US',\n-            formatingOptions,\n-          )})`;\n-        } else {\n-          changeStr = `${(entry.change / 100).toLocaleString(\n-            'en-US',\n-            formatingOptions,\n-          )} `;\n-        }\n-      } else if (currency === 'EUR') {\n-        let formatingOptions = {\n-          style: 'currency',\n-          currency: 'EUR',\n-          minimumFractionDigits: 2,\n-          maximumFractionDigits: 2,\n-        };\n-        if (entry.change < 0) {\n-          changeStr = `(${Math.abs(entry.change / 100).toLocaleString(\n-            'en-US',\n-            formatingOptions,\n-          )})`;\n-        } else {\n-          changeStr = `${(entry.change / 100).toLocaleString(\n-            'en-US',\n-            formatingOptions,\n-          )} `;\n-        }\n-      }\n-      table += changeStr.padStart(13, ' ');\n-      table += '\\n';\n-    });\n+function formatDate(date, locale) {\n+  if (locale === 'en-US') {\n+    // Format as mm/dd/yyyy\n+    const month = (date.getMonth() + 1).toString().padStart(2, '0');\n+    const day = date.getDate().toString().padStart(2, '0');\n+    const year = date.getFullYear();\n+    return `${month}/${day}/${year}`;\n   } else if (locale === 'nl-NL') {\n-    // Generate Header Row\n-    table +=\n-      'Datum'.padEnd(10, ' ') +\n-      ' | ' +\n-      'Omschrijving'.padEnd(25, ' ') +\n-      ' | ' +\n-      'Verandering'.padEnd(13, ' ') +\n-      '\\n';\n-\n-    // Sort entries\n-    entries.sort(\n-      (a, b) =>\n-        a.date - b.date ||\n-        a.change - b.change ||\n-        a.description.localeCompare(b.description),\n-    );\n+    // Format as dd-mm-yyyy\n+    const day = date.getDate().toString().padStart(2, '0');\n+    const month = (date.getMonth() + 1).toString().padStart(2, '0');\n+    const year = date.getFullYear();\n+    return `${day}-${month}-${year}`;\n+  }\n+  return '';\n+}\n \n-    entries.forEach((entry) => {\n-      // Write entry date to table\n-      const dateStr = `${entry.date.getDate().toString().padStart(2, '0')}-${(\n-        entry.date.getMonth() + 1\n-      )\n-        .toString()\n-        .padStart(2, '0')}-${entry.date.getFullYear()}`;\n-      table += `${dateStr} | `;\n+function formatDescription(description) {\n+  if (description.length > 25) {\n+    return `${description.substring(0, 22)}...`;\n+  }\n+  return description.padEnd(25, ' ');\n+}\n \n-      // Write entry description to table\n-      const truncatedDescription =\n-        entry.description.length > 25\n-          ? `${entry.description.substring(0, 22)}...`\n-          : entry.description.padEnd(25, ' ');\n-      table += `${truncatedDescription} | `;\n+function formatChange(change, currency, locale) {\n+  let formattingOptions;\n+  let formatted;\n \n-      // Write entry change to table\n-      let changeStr = '';\n-      if (currency === 'USD') {\n-        let formatingOptions = {\n-          style: 'currency',\n-          currency: 'USD',\n-          currencyDisplay: 'narrowSymbol',\n-          minimumFractionDigits: 2,\n-          maximumFractionDigits: 2,\n-        };\n-        changeStr = `${(entry.change / 100).toLocaleString(\n-          'nl-NL',\n-          formatingOptions,\n-        )} `;\n-      } else if (currency === 'EUR') {\n-        let formatingOptions = {\n-          style: 'currency',\n-          currency: 'EUR',\n-          currencyDisplay: 'narrowSymbol',\n-          minimumFractionDigits: 2,\n-          maximumFractionDigits: 2,\n-        };\n-        changeStr = `${(entry.change / 100).toLocaleString(\n-          'nl-NL',\n-          formatingOptions,\n-        )} `;\n-      }\n-      table += changeStr.padStart(13, ' ');\n-      table += '\\n';\n-    });\n+  if (currency === 'USD') {\n+    formattingOptions = {\n+      style: 'currency',\n+      currency: 'USD',\n+      minimumFractionDigits: 2,\n+      maximumFractionDigits: 2,\n+    };\n+    if (locale === 'en-US' && change < 0) {\n+      formatted = `(${Math.abs(change / 100).toLocaleString('en-US', formattingOptions)})`;\n+    } else {\n+      const localeUsed = locale === 'en-US' ? 'en-US' : 'nl-NL';\n+      formatted = `${(change / 100).toLocaleString(localeUsed, formattingOptions)} `;\n+    }\n+  } else if (currency === 'EUR') {\n+    formattingOptions = {\n+      style: 'currency',\n+      currency: 'EUR',\n+      minimumFractionDigits: 2,\n+      maximumFractionDigits: 2,\n+    };\n+    if (locale === 'en-US' && change < 0) {\n+      formatted = `(${Math.abs(change / 100).toLocaleString('en-US', formattingOptions)})`;\n+    } else {\n+      const localeUsed = locale === 'en-US' ? 'en-US' : 'nl-NL';\n+      formatted = `${(change / 100).toLocaleString(localeUsed, formattingOptions)} `;\n+    }\n   }\n+  // Ensure the change field is padded to a width of 13\n+  return formatted.padStart(13, ' ');\n+}\n+\n+export function formatEntries(currency, locale, entries) {\n+  let table = '';\n+  table += formatHeader(locale);\n+  sortEntries(entries);\n+  \n+  entries.forEach((entry) => {\n+    const dateStr = formatDate(entry.date, locale);\n+    const descriptionStr = formatDescription(entry.description);\n+    const changeStr = formatChange(entry.change, currency, locale);\n+    table += `${dateStr} | ${descriptionStr} | ${changeStr}\\n`;\n+  });\n+  \n   return table.replace(/\\n$/, '');\n }\n", "proposed_model_patches": [], "eval_result": "unresolved", "success": true}