{"instance_id": "java__variable-length-quantity", "model_name_or_path": "20250510_021328--claude-3-5-sonnet-20241022_0", "model_patch": "diff --git a/src/main/java/VariableLengthQuantity.java b/src/main/java/VariableLengthQuantity.java\nindex 59e45e0..a51ebb7 100644\n--- a/src/main/java/VariableLengthQuantity.java\n+++ b/src/main/java/VariableLengthQuantity.java\n@@ -1,12 +1,74 @@\n+import java.util.ArrayList;\n import java.util.List;\n \n class VariableLengthQuantity {\n \n+    // Encodes a list of 32-bit unsigned integers (represented as Long) into their VLQ byte sequences.\n+    // Each encoded number is represented as a string of space-separated two-digit uppercase hexadecimal bytes.\n     List<String> encode(List<Long> numbers) {\n-        throw new UnsupportedOperationException(\"Delete this statement and write your own implementation.\");\n+        List<String> encodedResults = new ArrayList<>();\n+\n+        for (Long number : numbers) {\n+            // Special case for 0\n+            if (number == 0) {\n+                encodedResults.add(String.format(\"%02X\", 0));\n+                continue;\n+            }\n+            List<Long> groups = new ArrayList<>();\n+            long value = number;\n+            // Break the number into 7-bit groups (LSB first)\n+            while (true) {\n+                groups.add(value & 0x7F);\n+                if (value < 0x80) {\n+                    break;\n+                }\n+                value >>= 7;\n+            }\n+            // Reverse groups so that most significant comes first\n+            List<Long> reversed = new ArrayList<>();\n+            for (int i = groups.size() - 1; i >= 0; i--) {\n+                reversed.add(groups.get(i));\n+            }\n+            // Set the continuation bit (0x80) for all but the last group\n+            StringBuilder sb = new StringBuilder();\n+            for (int i = 0; i < reversed.size(); i++) {\n+                long b = reversed.get(i);\n+                if (i != reversed.size() - 1) {\n+                    b |= 0x80;\n+                }\n+                sb.append(String.format(\"%02X\", b));\n+                if (i != reversed.size() - 1) {\n+                    sb.append(\" \");\n+                }\n+            }\n+            encodedResults.add(sb.toString());\n+        }\n+        return encodedResults;\n     }\n \n+    // Decodes a list of bytes (each represented as a Long) which form one or more VLQ encoded integers,\n+    // and returns the decoded 32-bit numbers as 8-digit uppercase hexadecimal strings.\n+    // Each VLQ encoded integer is terminated by a byte with the most-significant bit not set.\n     List<String> decode(List<Long> bytes) {\n-        throw new UnsupportedOperationException(\"Delete this statement and write your own implementation.\");\n+        List<String> decodedNumbers = new ArrayList<>();\n+        long current = 0;\n+        boolean inProgress = false;\n+        for (Long b : bytes) {\n+            // Use only low 8 bits from the provided long\n+            int byteVal = (int) (b & 0xFF);\n+            current = (current << 7) | (byteVal & 0x7F);\n+            inProgress = true;\n+            // If the continuation bit is not set, then this is the end of one number\n+            if ((byteVal & 0x80) == 0) {\n+                // Format as 8-digit zero-padded hexadecimal\n+                decodedNumbers.add(String.format(\"%08X\", current));\n+                current = 0;\n+                inProgress = false;\n+            }\n+        }\n+        // Note: If there's an unfinished sequence (inProgress true) then input was malformed.\n+        // Depending on requirements, we might throw an exception, but here we'll ignore it.\n+        return decodedNumbers;\n     }\n+\n }\n", "proposed_model_patches": [], "eval_result": "unresolved", "success": true}