{"instance_id": "python__zipper", "model_name_or_path": "20250510_021328--claude-3-5-sonnet-20241022_0", "model_patch": "diff --git a/zipper.py b/zipper.py\nindex c4bf1dd..0b305ad 100644\n--- a/zipper.py\n+++ b/zipper.py\n@@ -1,28 +1,80 @@\n class Zipper:\n+    def __init__(self, focus, breadcrumbs):\n+        # focus is a tuple: (value, left, right)\n+        # breadcrumbs is a list of tuples, each representing the context from parent.\n+        # Each breadcrumb is a tuple: (direction, parent_value, other_child)\n+        # direction is 'L' if we came from the left (so other_child is parent's right child),\n+        # or 'R' if we came from the right (so other_child is parent's left child).\n+        self.focus = focus\n+        self.breadcrumbs = breadcrumbs\n+\n     @staticmethod\n     def from_tree(tree):\n-        pass\n+        \"\"\"Create a zipper with focus on the root node of the given binary tree.\n+           The tree is expected to be a tuple: (value, left, right) where left and right may be None or similar tuples.\"\"\"\n+        return Zipper(tree, [])\n \n     def value(self):\n-        pass\n+        \"\"\"Return the value at the focus node.\"\"\"\n+        return self.focus[0]\n \n-    def set_value(self):\n-        pass\n+    def set_value(self, new_value):\n+        \"\"\"Return a new zipper with the focus node's value replaced by new_value.\"\"\"\n+        # Create a new node with updated value; left and right remain the same.\n+        new_node = (new_value, self.focus[1], self.focus[2])\n+        return Zipper(new_node, self.breadcrumbs)\n \n     def left(self):\n-        pass\n+        \"\"\"Move the focus to the left child. Raise Exception if left child is None.\"\"\"\n+        left_child = self.focus[1]\n+        if left_child is None:\n+            raise Exception(\"Left child is None\")\n+        # When moving left, store a breadcrumb with direction 'L'.\n+        # Save parent's value and parent's right subtree (the sibling).\n+        new_breadcrumb = ('L', self.focus[0], self.focus[2])\n+        return Zipper(left_child, self.breadcrumbs + [new_breadcrumb])\n \n-    def set_left(self):\n-        pass\n+    def set_left(self, new_tree):\n+        \"\"\"Return a new zipper with the left child replaced by new_tree in the focus node.\"\"\"\n+        new_node = (self.focus[0], new_tree, self.focus[2])\n+        return Zipper(new_node, self.breadcrumbs)\n \n     def right(self):\n-        pass\n+        \"\"\"Move the focus to the right child. Raise Exception if right child is None.\"\"\"\n+        right_child = self.focus[2]\n+        if right_child is None:\n+            raise Exception(\"Right child is None\")\n+        # When moving right, store a breadcrumb with direction 'R'.\n+        # Save parent's value and parent's left subtree (the sibling).\n+        new_breadcrumb = ('R', self.focus[0], self.focus[1])\n+        return Zipper(right_child, self.breadcrumbs + [new_breadcrumb])\n \n-    def set_right(self):\n-        pass\n+    def set_right(self, new_tree):\n+        \"\"\"Return a new zipper with the right child replaced by new_tree in the focus node.\"\"\"\n+        new_node = (self.focus[0], self.focus[1], new_tree)\n+        return Zipper(new_node, self.breadcrumbs)\n \n     def up(self):\n-        pass\n+        \"\"\"Move the focus up to the parent node. Raise Exception if already at the root.\"\"\"\n+        if not self.breadcrumbs:\n+            raise Exception(\"Already at the root of the tree\")\n+        crumb = self.breadcrumbs[-1]\n+        new_breadcrumbs = self.breadcrumbs[:-1]\n+        if crumb[0] == 'L':\n+            # We came from the left child, so parent's left becomes the current focus.\n+            # The parent's right child is stored in the breadcrumb.\n+            parent_node = (crumb[1], self.focus, crumb[2])\n+        elif crumb[0] == 'R':\n+            # We came from the right child, so parent's right becomes the current focus.\n+            # The parent's left child is stored in the breadcrumb.\n+            parent_node = (crumb[1], crumb[2], self.focus)\n+        else:\n+            raise Exception(\"Invalid breadcrumb information\")\n+        return Zipper(parent_node, new_breadcrumbs)\n \n     def to_tree(self):\n-        pass\n+        \"\"\"Reconstruct the entire binary tree from the current zipper, returning the root node.\"\"\"\n+        zipper = self\n+        while zipper.breadcrumbs:\n+            zipper = zipper.up()\n+        return zipper.focus\n", "proposed_model_patches": [], "eval_result": "unresolved", "success": true}