{"instance_id": "javascript__word-search", "model_name_or_path": "20250510_021328--claude-3-5-sonnet-20241022_0", "model_patch": "diff --git a/word-search.js b/word-search.js\nindex 857c522..69eb72a 100644\n--- a/word-search.js\n+++ b/word-search.js\n@@ -1,15 +1,71 @@\n //\n-// This is only a SKELETON file for the 'Word Search' exercise. It's been provided as a\n-// convenience to get you started writing code faster.\n-//\n+// Implementation for the Word Search puzzle problem.\n+// Given a puzzle (an array of strings) and a word to find, return the location of the first and last letter of the word as 1-indexed coordinates.\n+// The word can be found in any of the 8 directions: horizontal (left-to-right and right-to-left), vertical (top-to-bottom and bottom-to-top), and both diagonals.\n \n class WordSearch {\n-  constructor() {\n-    throw new Error('Remove this statement and implement this function');\n+  constructor(puzzle) {\n+    // puzzle is expected to be an array of strings, each representing a row of the grid.\n+    if (!Array.isArray(puzzle) || puzzle.length === 0) {\n+      throw new Error('Puzzle must be a non-empty array of strings');\n+    }\n+    this.grid = puzzle.map(row => row.split(''));\n+    this.rows = this.grid.length;\n+    this.cols = this.grid[0].length;\n   }\n \n-  find() {\n-    throw new Error('Remove this statement and implement this function');\n+  find(word) {\n+    if (typeof word !== 'string' || word.length === 0) {\n+      throw new Error('Word must be a non-empty string');\n+    }\n+\n+    const directions = [\n+      [-1, 0], // up\n+      [1, 0],  // down\n+      [0, -1], // left\n+      [0, 1],  // right\n+      [-1, -1], // up-left\n+      [-1, 1],  // up-right\n+      [1, -1], // down-left\n+      [1, 1]   // down-right\n+    ];\n+    \n+    const wordLen = word.length;\n+    // Convert word to lower case for case-insensitive comparison\n+    const lowerWord = word.toLowerCase();\n+    \n+    for (let i = 0; i < this.rows; i++) {\n+      for (let j = 0; j < this.cols; j++) {\n+        // Check if the current cell can be a starting letter\n+        if (this.grid[i][j].toLowerCase() === lowerWord[0]) {\n+          // Try each direction\n+          for (const [dx, dy] of directions) {\n+            let k;\n+            for (k = 0; k < wordLen; k++) {\n+              const newX = i + dx * k;\n+              const newY = j + dy * k;\n+              // Check if new position is within bounds\n+              if (newX < 0 || newX >= this.rows || newY < 0 || newY >= this.cols) {\n+                break;\n+              }\n+              if (this.grid[newX][newY].toLowerCase() !== lowerWord[k]) {\n+                break;\n+              }\n+            }\n+            if (k === wordLen) {\n+              // If we made it through the word, the word is found.\n+              // Return coordinates as 1-indexed [row, col] for first and last letter\n+              const start = [i + 1, j + 1];\n+              const end = [i + dx * (wordLen - 1) + 1, j + dy * (wordLen - 1) + 1];\n+              return [start, end];\n+            }\n+          }\n+        }\n+      }\n+    }\n+    \n+    // If no match is found, throw an error\n+    throw new Error('Word not found');\n   }\n }\n \n", "proposed_model_patches": [], "eval_result": "unresolved", "success": true}