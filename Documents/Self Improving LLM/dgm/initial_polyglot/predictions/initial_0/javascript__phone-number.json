{"instance_id": "javascript__phone-number", "model_name_or_path": "20250510_021328--claude-3-5-sonnet-20241022_0", "model_patch": "diff --git a/phone-number.js b/phone-number.js\nindex ba60516..204a7be 100644\n--- a/phone-number.js\n+++ b/phone-number.js\n@@ -1,8 +1,54 @@\n-//\n-// This is only a SKELETON file for the 'Phone Number' exercise. It's been provided as a\n-// convenience to get you started writing code faster.\n-//\n+/*\n+ * Clean up user-entered phone numbers so that they can be sent SMS messages.\n+ *\n+ * The North American Numbering Plan (NANP) is a telephone numbering system used by many countries in North America like the United States, Canada or Bermuda.\n+ * All NANP-countries share the same international country code: 1.\n+ *\n+ * NANP numbers are ten-digit numbers consisting of a three-digit Numbering Plan Area code (area code) followed by the seven-digit local number.\n+ * The first three digits of the local number represent the exchange code, followed by the unique four-digit subscriber number.\n+ *\n+ * The format is usually represented as:\n+ *\n+ *   NXX NXX-XXXX\n+ *\n+ * where \"N\" is any digit from 2 through 9 and \"X\" is any digit from 0 through 9.\n+ *\n+ * Sometimes they also have the country code (represented as \"1\" or \"+1\") prefixed.\n+ *\n+ * This function will clean up differently formatted telephone numbers by removing all non-digit characters\n+ * and the country code if present. It also validates that the number conforms to NANP rules.\n+ *\n+ * If the input is invalid (wrong number of digits after cleaning, or if the area code or exchange code\n+ * does not begin with a digit in the range 2-9) then the function throws an error.\n+ */\n \n-export const clean = () => {\n-  throw new Error('Remove this statement and implement this function');\n+export const clean = (input) => {\n+  // Remove all non-digit characters\n+  let digits = input.replace(/\\D/g, '');\n+\n+  // Handle country code if present\n+  if (digits.length === 11) {\n+    if (digits[0] === '1') {\n+      digits = digits.slice(1);\n+    } else {\n+      throw new Error('incorrect number');\n+    }\n+  }\n+\n+  // Check for exactly 10 digits\n+  if (digits.length !== 10) {\n+    throw new Error('incorrect number');\n+  }\n+\n+  // The first digit of the area code (digits[0]) must be 2-9\n+  if (!/^[2-9]/.test(digits)) {\n+    throw new Error('incorrect number');\n+  }\n+\n+  // The first digit of the exchange code (digits[3]) must be 2-9\n+  if (!/^[2-9]/.test(digits.slice(3, 4))) {\n+    throw new Error('incorrect number');\n+  }\n+\n+  return digits;\n };\n", "proposed_model_patches": [], "eval_result": "unresolved", "success": true}