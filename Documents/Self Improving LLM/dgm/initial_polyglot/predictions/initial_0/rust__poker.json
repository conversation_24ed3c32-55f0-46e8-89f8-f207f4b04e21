{"instance_id": "rust__poker", "model_name_or_path": "20250510_021328--claude-3-5-sonnet-20241022_0", "model_patch": "diff --git a/src/lib.rs b/src/lib.rs\nindex a6f117e..672aca2 100644\n--- a/src/lib.rs\n+++ b/src/lib.rs\n@@ -1,7 +1,155 @@\n+use std::collections::HashMap;\n+\n /// Given a list of poker hands, return a list of those hands which win.\n ///\n /// Note the type signature: this function should return _the same_ reference to\n /// the winning hand(s) as were passed in, not reconstructed strings which happen to be equal.\n pub fn winning_hands<'a>(hands: &[&'a str]) -> Vec<&'a str> {\n-    todo!(\"Out of {hands:?}, which hand wins?\")\n+    // Helper function to evaluate a hand.\n+    // Returns a tuple of (category, tie_breakers).\n+    // Categories:\n+    // 8: Straight Flush\n+    // 7: Four of a Kind\n+    // 6: Full House\n+    // 5: Flush\n+    // 4: Straight\n+    // 3: Three of a Kind\n+    // 2: Two Pair\n+    // 1: One Pair\n+    // 0: High Card\n+    \n+    fn evaluate_hand(hand: &str) -> (u8, Vec<u8>) {\n+        // Parse cards\n+        let cards: Vec<&str> = hand.split_whitespace().collect();\n+        let mut ranks = Vec::new();\n+        let mut suits = Vec::new();\n+        for card in cards.iter() {\n+            let mut chars = card.chars();\n+            let rank_char = chars.next().expect(\"Card has no rank\");\n+            let suit_char = chars.next().expect(\"Card has no suit\");\n+            let rank = match rank_char {\n+                '2'..='9' => rank_char.to_digit(10).unwrap() as u8,\n+                'T' => 10,\n+                'J' => 11,\n+                'Q' => 12,\n+                'K' => 13,\n+                'A' => 14,\n+                _ => panic!(\"Invalid rank: {}\", rank_char),\n+            };\n+            ranks.push(rank);\n+            suits.push(suit_char);\n+        }\n+\n+        // Check for flush: all suits the same\n+        let flush = suits.iter().all(|&s| s == suits[0]);\n+\n+        // Sort ranks to check for straight.\n+        ranks.sort_unstable();\n+        let is_normal_straight = ranks.windows(2).all(|w| w[1] == w[0] + 1);\n+        // Ace-low straight: 2,3,4,5,A\n+        let is_ace_low = ranks == vec![2, 3, 4, 5, 14];\n+        let straight = is_normal_straight || is_ace_low;\n+        let straight_high = if is_ace_low { 5 } else { *ranks.last().unwrap() };\n+\n+        // Build frequency count for ranks\n+        let mut freq = HashMap::new();\n+        for &r in &ranks {\n+            *freq.entry(r).or_insert(0) += 1;\n+        }\n+        // Create vector of (rank, count) pairs and sort by count descending, then rank descending\n+        let mut freq_vec: Vec<(u8, u8)> = freq.into_iter().collect();\n+        freq_vec.sort_by(|&(r1, count1), &(r2, count2)| {\n+            count2.cmp(&count1).then(r2.cmp(&r1))\n+        });\n+\n+        // Straight Flush\n+        if straight && flush {\n+            return (8, vec![straight_high]);\n+        }\n+        \n+        // Four of a Kind\n+        if freq_vec[0].1 == 4 {\n+            let four = freq_vec[0].0;\n+            let kicker = freq_vec[1].0;\n+            return (7, vec![four, kicker]);\n+        }\n+        \n+        // Full House: three of a kind and a pair\n+        if freq_vec[0].1 == 3 && freq_vec.len() == 2 {\n+            let three = freq_vec[0].0;\n+            let pair = freq_vec[1].0;\n+            return (6, vec![three, pair]);\n+        }\n+        \n+        // Flush\n+        if flush {\n+            let mut sorted_desc = ranks.clone();\n+            sorted_desc.sort_unstable_by(|a, b| b.cmp(a));\n+            return (5, sorted_desc);\n+        }\n+        \n+        // Straight\n+        if straight {\n+            return (4, vec![straight_high]);\n+        }\n+        \n+        // Three of a Kind\n+        if freq_vec[0].1 == 3 {\n+            let three = freq_vec[0].0;\n+            let mut kickers: Vec<u8> = ranks.iter().copied().filter(|&r| r != three).collect();\n+            kickers.sort_unstable_by(|a, b| b.cmp(a));\n+            let mut tie = vec![three];\n+            tie.extend(kickers);\n+            return (3, tie);\n+        }\n+        \n+        // Count pairs\n+        let pairs: Vec<u8> = freq_vec.iter()\n+            .filter(|&&(_, count)| count == 2)\n+            .map(|&(r, _)| r)\n+            .collect();\n+        \n+        // Two Pair\n+        if pairs.len() == 2 {\n+            let mut sorted_pairs = pairs;\n+            sorted_pairs.sort_unstable_by(|a, b| b.cmp(a));\n+            // The kicker is the card not in the pairs\n+            let kicker = *ranks.iter().find(|&&r| !sorted_pairs.contains(&r)).unwrap();\n+            return (2, [sorted_pairs, vec![kicker]].concat());\n+        }\n+        \n+        // One Pair\n+        if pairs.len() == 1 {\n+            let pair = pairs[0];\n+            let mut kickers: Vec<u8> = ranks.iter().copied().filter(|&r| r != pair).collect();\n+            kickers.sort_unstable_by(|a, b| b.cmp(a));\n+            return (1, [vec![pair], kickers].concat());\n+        }\n+        \n+        // High Card\n+        let mut sorted_desc = ranks.clone();\n+        sorted_desc.sort_unstable_by(|a, b| b.cmp(a));\n+        (0, sorted_desc)\n+    }\n+\n+    // Evaluate each hand, mapping hand reference to its evaluation.\n+    let evaluated: Vec<(&'a str, (u8, Vec<u8>))> = hands.iter()\n+        .map(|&hand| (hand, evaluate_hand(hand)))\n+        .collect();\n+\n+    // Determine the best hand evaluation using lexicographical order: first category, then tiebreakers\n+    let best_eval = evaluated.iter().max_by(|(_, eval_a), (_, eval_b)| {\n+        let (cat_a, tiebreakers_a) = eval_a;\n+        let (cat_b, tiebreakers_b) = eval_b;\n+        match cat_a.cmp(cat_b) {\n+            std::cmp::Ordering::Equal => tiebreakers_a.cmp(tiebreakers_b),\n+            ordering => ordering,\n+        }\n+    }).expect(\"No hands provided\").1.clone();\n+\n+    // Return all hands that match the best evaluation, preserving original reference\n+    evaluated.into_iter()\n+        .filter(|&(_, ref eval)| eval == &best_eval)\n+        .map(|(hand, _)| hand)\n+        .collect()\n }\n", "proposed_model_patches": [], "eval_result": "unresolved", "success": true}