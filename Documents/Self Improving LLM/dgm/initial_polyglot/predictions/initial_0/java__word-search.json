{"instance_id": "java__word-search", "model_name_or_path": "20250510_021328--claude-3-5-sonnet-20241022_0", "model_patch": "diff --git a/src/main/java/WordSearcher.java b/src/main/java/WordSearcher.java\nindex 20ebc08..4e3c73c 100644\n--- a/src/main/java/WordSearcher.java\n+++ b/src/main/java/WordSearcher.java\n@@ -1,9 +1,95 @@\n import java.util.Map;\n+import java.util.HashMap;\n import java.util.Optional;\n import java.util.Set;\n \n class WordSearcher {\n+    // Directions: right, left, down, up, diag down-right, diag down-left, diag up-right, diag up-left\n+    private static final int[][] DIRECTIONS = {\n+        {0, 1},  // right\n+        {0, -1}, // left\n+        {1, 0},  // down\n+        {-1, 0}, // up\n+        {1, 1},  // diag down-right\n+        {1, -1}, // diag down-left\n+        {-1, 1}, // diag up-right\n+        {-1, -1} // diag up-left\n+    };\n+\n     Map<String, Optional<WordLocation>> search(final Set<String> words, final char[][] grid) {\n-        throw new UnsupportedOperationException(\"Please implement the WordSearcher.search() method.\");\n+        Map<String, Optional<WordLocation>> result = new HashMap<>();\n+        int rows = grid.length;\n+        if (rows == 0) return result;\n+        int cols = grid[0].length;\n+\n+        for (String word : words) {\n+            Optional<WordLocation> loc = findWord(grid, rows, cols, word);\n+            result.put(word, loc);\n+        }\n+        return result;\n+    }\n+\n+    private Optional<WordLocation> findWord(char[][] grid, int rows, int cols, String word) {\n+        int len = word.length();\n+        if (len == 0) return Optional.empty();\n+\n+        // Try each cell as possible starting point\n+        for (int row = 0; row < rows; row++) {\n+            for (int col = 0; col < cols; col++) {\n+                // Check for forward match if grid cell matches first char of word\n+                if (grid[row][col] == word.charAt(0)) {\n+                    for (int[] dir : DIRECTIONS) {\n+                        int dRow = dir[0], dCol = dir[1];\n+                        int endRow = row + dRow * (len - 1);\n+                        int endCol = col + dCol * (len - 1);\n+                        // check if end indices are within bounds\n+                        if (endRow < 0 || endRow >= rows || endCol < 0 || endCol >= cols) {\n+                            continue;\n+                        }\n+                        boolean match = true;\n+                        for (int k = 0; k < len; k++) {\n+                            int curRow = row + dRow * k;\n+                            int curCol = col + dCol * k;\n+                            if (grid[curRow][curCol] != word.charAt(k)) {\n+                                match = false;\n+                                break;\n+                            }\n+                        }\n+                        if (match) {\n+                            // Return coordinates as 1-indexed\n+                            return Optional.of(new WordLocation(row + 1, col + 1, endRow + 1, endCol + 1));\n+                        }\n+                    }\n+                }\n+                // Check for backward match if the cell matches the last char of word\n+                if (grid[row][col] == word.charAt(len - 1)) {\n+                    for (int[] dir : DIRECTIONS) {\n+                        int dRow = dir[0], dCol = dir[1];\n+                        int endRow = row + dRow * (len - 1);\n+                        int endCol = col + dCol * (len - 1);\n+                        if (endRow < 0 || endRow >= rows || endCol < 0 || endCol >= cols) {\n+                            continue;\n+                        }\n+                        boolean match = true;\n+                        // Check in the direction for the reversed word\n+                        for (int k = 0; k < len; k++) {\n+                            int curRow = row + dRow * k;\n+                            int curCol = col + dCol * k;\n+                            if (grid[curRow][curCol] != word.charAt(len - 1 - k)) {\n+                                match = false;\n+                                break;\n+                            }\n+                        }\n+                        if (match) {\n+                            // For a backward match, the grid sequence is reversed relative to the given word.\n+                            // We need to return the location corresponding to the first letter of the provided word.\n+                            // That is, the cell at the end of the matched segment.\n+                            return Optional.of(new WordLocation(endRow + 1, endCol + 1, row + 1, col + 1));\n+                        }\n+                    }\n+                }\n+            }\n+        }\n+        return Optional.empty();\n     }\n }\ndiff --git a/src/main/java/WordLocation.java b/src/main/java/WordLocation.java\nnew file mode 100644\nindex 0000000..5270897\n--- /dev/null\n+++ b/src/main/java/WordLocation.java\n@@ -0,0 +1,34 @@\n+public class WordLocation {\n+    private final int startRow;\n+    private final int startCol;\n+    private final int endRow;\n+    private final int endCol;\n+\n+    public WordLocation(int startRow, int startCol, int endRow, int endCol) {\n+        this.startRow = startRow;\n+        this.startCol = startCol;\n+        this.endRow = endRow;\n+        this.endCol = endCol;\n+    }\n+\n+    public int getStartRow() {\n+        return startRow;\n+    }\n+\n+    public int getStartCol() {\n+        return startCol;\n+    }\n+\n+    public int getEndRow() {\n+        return endRow;\n+    }\n+\n+    public int getEndCol() {\n+        return endCol;\n+    }\n+\n+    @Override\n+    public String toString() {\n+        return \"(\" + startRow + \", \" + startCol + \") -> (\" + endRow + \", \" + endCol + \")\";\n+    }\n+}\n", "proposed_model_patches": [], "eval_result": "unresolved", "success": true}