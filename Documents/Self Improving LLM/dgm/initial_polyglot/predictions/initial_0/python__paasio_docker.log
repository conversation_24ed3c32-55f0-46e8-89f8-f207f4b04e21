2025-05-10 02:39:40,278 - ThreadPoolExecutor-0_17 - INFO - No existing container with name pb.eval.python__paasio.20250510_023940_090074 found.
2025-05-10 02:39:40,299 - ThreadPoolExecutor-0_17 - INFO - Environment image pb.env.x86_64.ce9e95520b42abb86d1825:latest found for python__paasio
Building instance image pb.eval.x86_64.python__paasio:latest for python__paasio
2025-05-10 02:39:40,330 - ThreadPoolExecutor-0_17 - INFO - Image pb.eval.x86_64.python__paasio:latest already exists, skipping build.
2025-05-10 02:39:40,338 - ThreadPoolExecutor-0_17 - INFO - Creating container for python__paasio...
2025-05-10 02:39:42,160 - ThreadPoolExecutor-0_17 - INFO - Container for python__paasio created: fa5c5ffaa8e93d76bedf7057c75d81f77d3625b7402e558201411e9f6c5ebc07
2025-05-10 02:39:45,850 - ThreadPoolExecutor-0_17 - INFO - Copying coding_agent.py to container at /dgm/coding_agent.py
2025-05-10 02:39:45,911 - ThreadPoolExecutor-0_17 - INFO - Successfully copied coding_agent.py to container
2025-05-10 02:39:46,942 - ThreadPoolExecutor-0_17 - INFO - Copying requirements.txt to container at /dgm/requirements.txt
2025-05-10 02:39:47,119 - ThreadPoolExecutor-0_17 - INFO - Successfully copied requirements.txt to container
2025-05-10 02:39:47,877 - ThreadPoolExecutor-0_17 - INFO - Copying pytest.ini to container at /dgm/pytest.ini
2025-05-10 02:39:47,929 - ThreadPoolExecutor-0_17 - INFO - Successfully copied pytest.ini to container
2025-05-10 02:39:48,811 - ThreadPoolExecutor-0_17 - INFO - Copying tools to container at /dgm/tools
2025-05-10 02:39:48,868 - ThreadPoolExecutor-0_17 - INFO - Successfully copied tools to container
2025-05-10 02:39:49,722 - ThreadPoolExecutor-0_17 - INFO - Copying utils to container at /dgm/utils
2025-05-10 02:39:50,133 - ThreadPoolExecutor-0_17 - INFO - Successfully copied utils to container
2025-05-10 02:39:50,606 - ThreadPoolExecutor-0_17 - INFO - Copying tests to container at /dgm/tests
2025-05-10 02:39:50,667 - ThreadPoolExecutor-0_17 - INFO - Successfully copied tests to container
2025-05-10 02:39:51,111 - ThreadPoolExecutor-0_17 - INFO - Copying prompts to container at /dgm/prompts
2025-05-10 02:39:51,512 - ThreadPoolExecutor-0_17 - INFO - Successfully copied prompts to container
2025-05-10 02:39:52,071 - ThreadPoolExecutor-0_17 - INFO - Copying llm.py to container at /dgm/llm.py
2025-05-10 02:39:52,138 - ThreadPoolExecutor-0_17 - INFO - Successfully copied llm.py to container
2025-05-10 02:39:52,651 - ThreadPoolExecutor-0_17 - INFO - Copying llm_withtools.py to container at /dgm/llm_withtools.py
2025-05-10 02:39:52,776 - ThreadPoolExecutor-0_17 - INFO - Successfully copied llm_withtools.py to container
2025-05-10 02:39:53,514 - ThreadPoolExecutor-0_17 - INFO - Container output: /testbed:
paasio.py

2025-05-10 02:39:53,514 - ThreadPoolExecutor-0_17 - INFO - Installing more requirements
2025-05-10 02:43:49,622 - ThreadPoolExecutor-0_17 - INFO - Container output: Collecting datasets (from -r /dgm/requirements.txt (line 1))
  Downloading datasets-3.6.0-py3-none-any.whl.metadata (19 kB)
Collecting anthropic (from -r /dgm/requirements.txt (line 2))
  Downloading anthropic-0.51.0-py3-none-any.whl.metadata (25 kB)
Collecting backoff (from -r /dgm/requirements.txt (line 4))
  Downloading backoff-2.2.1-py3-none-any.whl.metadata (14 kB)
Collecting botocore (from -r /dgm/requirements.txt (line 5))
  Downloading botocore-1.38.13-py3-none-any.whl.metadata (5.7 kB)
Collecting boto3 (from -r /dgm/requirements.txt (line 6))
  Downloading boto3-1.38.13-py3-none-any.whl.metadata (6.6 kB)
Collecting openai (from -r /dgm/requirements.txt (line 7))
  Downloading openai-1.78.0-py3-none-any.whl.metadata (25 kB)
Collecting beautifulsoup4 (from -r /dgm/requirements.txt (line 10))
  Downloading beautifulsoup4-4.13.4-py3-none-any.whl.metadata (3.8 kB)
Collecting chardet (from -r /dgm/requirements.txt (line 11))
  Downloading chardet-5.2.0-py3-none-any.whl.metadata (3.4 kB)
Collecting docker (from -r /dgm/requirements.txt (line 12))
  Downloading docker-7.1.0-py3-none-any.whl.metadata (3.8 kB)
Collecting ghapi (from -r /dgm/requirements.txt (line 13))
  Downloading ghapi-1.0.6-py3-none-any.whl.metadata (13 kB)
Collecting GitPython (from -r /dgm/requirements.txt (line 14))
  Downloading GitPython-3.1.44-py3-none-any.whl.metadata (13 kB)
Collecting pre-commit (from -r /dgm/requirements.txt (line 15))
  Downloading pre_commit-4.2.0-py2.py3-none-any.whl.metadata (1.3 kB)
Collecting python-dotenv (from -r /dgm/requirements.txt (line 16))
  Downloading python_dotenv-1.1.0-py3-none-any.whl.metadata (24 kB)
Collecting rich (from -r /dgm/requirements.txt (line 17))
  Downloading rich-14.0.0-py3-none-any.whl.metadata (18 kB)
Collecting unidiff (from -r /dgm/requirements.txt (line 18))
  Downloading unidiff-0.7.5-py2.py3-none-any.whl.metadata (4.6 kB)
Collecting pytest (from -r /dgm/requirements.txt (line 21))
  Using cached pytest-8.3.5-py3-none-any.whl.metadata (7.6 kB)
Collecting pytest-asyncio (from -r /dgm/requirements.txt (line 22))
  Downloading pytest_asyncio-0.26.0-py3-none-any.whl.metadata (4.0 kB)
Collecting async_timeout (from -r /dgm/requirements.txt (line 23))
  Using cached async_timeout-5.0.1-py3-none-any.whl.metadata (5.1 kB)
Collecting filelock (from datasets->-r /dgm/requirements.txt (line 1))
  Downloading filelock-3.18.0-py3-none-any.whl.metadata (2.9 kB)
Collecting numpy>=1.17 (from datasets->-r /dgm/requirements.txt (line 1))
  Downloading numpy-2.2.5-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (62 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 62.0/62.0 kB 302.3 kB/s eta 0:00:00
Collecting pyarrow>=15.0.0 (from datasets->-r /dgm/requirements.txt (line 1))
  Downloading pyarrow-20.0.0-cp311-cp311-manylinux_2_28_x86_64.whl.metadata (3.3 kB)
Collecting dill<0.3.9,>=0.3.0 (from datasets->-r /dgm/requirements.txt (line 1))
  Downloading dill-0.3.8-py3-none-any.whl.metadata (10 kB)
Collecting pandas (from datasets->-r /dgm/requirements.txt (line 1))
  Downloading pandas-2.2.3-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (89 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 89.9/89.9 kB 1.1 MB/s eta 0:00:00
Collecting requests>=2.32.2 (from datasets->-r /dgm/requirements.txt (line 1))
  Downloading requests-2.32.3-py3-none-any.whl.metadata (4.6 kB)
Collecting tqdm>=4.66.3 (from datasets->-r /dgm/requirements.txt (line 1))
  Downloading tqdm-4.67.1-py3-none-any.whl.metadata (57 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 57.7/57.7 kB 2.2 MB/s eta 0:00:00
Collecting xxhash (from datasets->-r /dgm/requirements.txt (line 1))
  Downloading xxhash-3.5.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (12 kB)
Collecting multiprocess<0.70.17 (from datasets->-r /dgm/requirements.txt (line 1))
  Downloading multiprocess-0.70.16-py311-none-any.whl.metadata (7.2 kB)
Collecting fsspec<=2025.3.0,>=2023.1.0 (from fsspec[http]<=2025.3.0,>=2023.1.0->datasets->-r /dgm/requirements.txt (line 1))
  Downloading fsspec-2025.3.0-py3-none-any.whl.metadata (11 kB)
Collecting huggingface-hub>=0.24.0 (from datasets->-r /dgm/requirements.txt (line 1))
  Downloading huggingface_hub-0.31.1-py3-none-any.whl.metadata (13 kB)
Requirement already satisfied: packaging in /opt/miniconda3/lib/python3.11/site-packages (from datasets->-r /dgm/requirements.txt (line 1)) (23.1)
Collecting pyyaml>=5.1 (from datasets->-r /dgm/requirements.txt (line 1))
  Downloading PyYAML-6.0.2-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (2.1 kB)
Collecting anyio<5,>=3.5.0 (from anthropic->-r /dgm/requirements.txt (line 2))
  Downloading anyio-4.9.0-py3-none-any.whl.metadata (4.7 kB)
Requirement already satisfied: distro<2,>=1.7.0 in /opt/miniconda3/lib/python3.11/site-packages (from anthropic->-r /dgm/requirements.txt (line 2)) (1.8.0)
Collecting httpx<1,>=0.25.0 (from anthropic->-r /dgm/requirements.txt (line 2))
  Downloading httpx-0.28.1-py3-none-any.whl.metadata (7.1 kB)
Collecting jiter<1,>=0.4.0 (from anthropic->-r /dgm/requirements.txt (line 2))
  Downloading jiter-0.9.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (5.2 kB)
Collecting pydantic<3,>=1.9.0 (from anthropic->-r /dgm/requirements.txt (line 2))
  Downloading pydantic-2.11.4-py3-none-any.whl.metadata (66 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 66.6/66.6 kB 605.5 kB/s eta 0:00:00
Collecting sniffio (from anthropic->-r /dgm/requirements.txt (line 2))
  Using cached sniffio-1.3.1-py3-none-any.whl.metadata (3.9 kB)
Collecting typing-extensions<5,>=4.10 (from anthropic->-r /dgm/requirements.txt (line 2))
  Downloading typing_extensions-4.13.2-py3-none-any.whl.metadata (3.0 kB)
Collecting jmespath<2.0.0,>=0.7.1 (from botocore->-r /dgm/requirements.txt (line 5))
  Downloading jmespath-1.0.1-py3-none-any.whl.metadata (7.6 kB)
Collecting python-dateutil<3.0.0,>=2.1 (from botocore->-r /dgm/requirements.txt (line 5))
  Downloading python_dateutil-2.9.0.post0-py2.py3-none-any.whl.metadata (8.4 kB)
Requirement already satisfied: urllib3!=2.2.0,<3,>=1.25.4 in /opt/miniconda3/lib/python3.11/site-packages (from botocore->-r /dgm/requirements.txt (line 5)) (1.26.18)
Collecting s3transfer<0.13.0,>=0.12.0 (from boto3->-r /dgm/requirements.txt (line 6))
  Downloading s3transfer-0.12.0-py3-none-any.whl.metadata (1.7 kB)
Collecting soupsieve>1.2 (from beautifulsoup4->-r /dgm/requirements.txt (line 10))
  Downloading soupsieve-2.7-py3-none-any.whl.metadata (4.6 kB)
Collecting fastcore>=1.7.2 (from ghapi->-r /dgm/requirements.txt (line 13))
  Downloading fastcore-1.8.2-py3-none-any.whl.metadata (3.7 kB)
Collecting gitdb<5,>=4.0.1 (from GitPython->-r /dgm/requirements.txt (line 14))
  Downloading gitdb-4.0.12-py3-none-any.whl.metadata (1.2 kB)
Collecting cfgv>=2.0.0 (from pre-commit->-r /dgm/requirements.txt (line 15))
  Downloading cfgv-3.4.0-py2.py3-none-any.whl.metadata (8.5 kB)
Collecting identify>=1.0.0 (from pre-commit->-r /dgm/requirements.txt (line 15))
  Downloading identify-2.6.10-py2.py3-none-any.whl.metadata (4.4 kB)
Collecting nodeenv>=0.11.1 (from pre-commit->-r /dgm/requirements.txt (line 15))
  Downloading nodeenv-1.9.1-py2.py3-none-any.whl.metadata (21 kB)
Collecting virtualenv>=20.10.0 (from pre-commit->-r /dgm/requirements.txt (line 15))
  Downloading virtualenv-20.31.2-py3-none-any.whl.metadata (4.5 kB)
Collecting markdown-it-py>=2.2.0 (from rich->-r /dgm/requirements.txt (line 17))
  Downloading markdown_it_py-3.0.0-py3-none-any.whl.metadata (6.9 kB)
Collecting pygments<3.0.0,>=2.13.0 (from rich->-r /dgm/requirements.txt (line 17))
  Downloading pygments-2.19.1-py3-none-any.whl.metadata (2.5 kB)
Collecting iniconfig (from pytest->-r /dgm/requirements.txt (line 21))
  Using cached iniconfig-2.1.0-py3-none-any.whl.metadata (2.7 kB)
Collecting pluggy<2,>=1.5 (from pytest->-r /dgm/requirements.txt (line 21))
  Using cached pluggy-1.5.0-py3-none-any.whl.metadata (4.8 kB)
Requirement already satisfied: idna>=2.8 in /opt/miniconda3/lib/python3.11/site-packages (from anyio<5,>=3.5.0->anthropic->-r /dgm/requirements.txt (line 2)) (3.4)
Collecting aiohttp!=4.0.0a0,!=4.0.0a1 (from fsspec[http]<=2025.3.0,>=2023.1.0->datasets->-r /dgm/requirements.txt (line 1))
  Downloading aiohttp-3.11.18-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (7.7 kB)
Collecting smmap<6,>=3.0.1 (from gitdb<5,>=4.0.1->GitPython->-r /dgm/requirements.txt (line 14))
  Downloading smmap-5.0.2-py3-none-any.whl.metadata (4.3 kB)
Requirement already satisfied: certifi in /opt/miniconda3/lib/python3.11/site-packages (from httpx<1,>=0.25.0->anthropic->-r /dgm/requirements.txt (line 2)) (2023.11.17)
Collecting httpcore==1.* (from httpx<1,>=0.25.0->anthropic->-r /dgm/requirements.txt (line 2))
  Downloading httpcore-1.0.9-py3-none-any.whl.metadata (21 kB)
Collecting h11>=0.16 (from httpcore==1.*->httpx<1,>=0.25.0->anthropic->-r /dgm/requirements.txt (line 2))
  Downloading h11-0.16.0-py3-none-any.whl.metadata (8.3 kB)
Collecting hf-xet<2.0.0,>=1.1.0 (from huggingface-hub>=0.24.0->datasets->-r /dgm/requirements.txt (line 1))
  Downloading hf_xet-1.1.0-cp37-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (494 bytes)
Collecting mdurl~=0.1 (from markdown-it-py>=2.2.0->rich->-r /dgm/requirements.txt (line 17))
  Downloading mdurl-0.1.2-py3-none-any.whl.metadata (1.6 kB)
Collecting annotated-types>=0.6.0 (from pydantic<3,>=1.9.0->anthropic->-r /dgm/requirements.txt (line 2))
  Downloading annotated_types-0.7.0-py3-none-any.whl.metadata (15 kB)
Collecting pydantic-core==2.33.2 (from pydantic<3,>=1.9.0->anthropic->-r /dgm/requirements.txt (line 2))
  Downloading pydantic_core-2.33.2-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (6.8 kB)
Collecting typing-inspection>=0.4.0 (from pydantic<3,>=1.9.0->anthropic->-r /dgm/requirements.txt (line 2))
  Downloading typing_inspection-0.4.0-py3-none-any.whl.metadata (2.6 kB)
Collecting six>=1.5 (from python-dateutil<3.0.0,>=2.1->botocore->-r /dgm/requirements.txt (line 5))
  Downloading six-1.17.0-py2.py3-none-any.whl.metadata (1.7 kB)
Requirement already satisfied: charset-normalizer<4,>=2 in /opt/miniconda3/lib/python3.11/site-packages (from requests>=2.32.2->datasets->-r /dgm/requirements.txt (line 1)) (2.0.4)
Collecting distlib<1,>=0.3.7 (from virtualenv>=20.10.0->pre-commit->-r /dgm/requirements.txt (line 15))
  Downloading distlib-0.3.9-py2.py3-none-any.whl.metadata (5.2 kB)
Requirement already satisfied: platformdirs<5,>=3.9.1 in /opt/miniconda3/lib/python3.11/site-packages (from virtualenv>=20.10.0->pre-commit->-r /dgm/requirements.txt (line 15)) (3.10.0)
Collecting pytz>=2020.1 (from pandas->datasets->-r /dgm/requirements.txt (line 1))
  Downloading pytz-2025.2-py2.py3-none-any.whl.metadata (22 kB)
Collecting tzdata>=2022.7 (from pandas->datasets->-r /dgm/requirements.txt (line 1))
  Downloading tzdata-2025.2-py2.py3-none-any.whl.metadata (1.4 kB)
Collecting aiohappyeyeballs>=2.3.0 (from aiohttp!=4.0.0a0,!=4.0.0a1->fsspec[http]<=2025.3.0,>=2023.1.0->datasets->-r /dgm/requirements.txt (line 1))
  Downloading aiohappyeyeballs-2.6.1-py3-none-any.whl.metadata (5.9 kB)
Collecting aiosignal>=1.1.2 (from aiohttp!=4.0.0a0,!=4.0.0a1->fsspec[http]<=2025.3.0,>=2023.1.0->datasets->-r /dgm/requirements.txt (line 1))
  Downloading aiosignal-1.3.2-py2.py3-none-any.whl.metadata (3.8 kB)
Collecting attrs>=17.3.0 (from aiohttp!=4.0.0a0,!=4.0.0a1->fsspec[http]<=2025.3.0,>=2023.1.0->datasets->-r /dgm/requirements.txt (line 1))
  Using cached attrs-25.3.0-py3-none-any.whl.metadata (10 kB)
Collecting frozenlist>=1.1.1 (from aiohttp!=4.0.0a0,!=4.0.0a1->fsspec[http]<=2025.3.0,>=2023.1.0->datasets->-r /dgm/requirements.txt (line 1))
  Downloading frozenlist-1.6.0-cp311-cp311-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (16 kB)
Collecting multidict<7.0,>=4.5 (from aiohttp!=4.0.0a0,!=4.0.0a1->fsspec[http]<=2025.3.0,>=2023.1.0->datasets->-r /dgm/requirements.txt (line 1))
  Downloading multidict-6.4.3-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (5.3 kB)
Collecting propcache>=0.2.0 (from aiohttp!=4.0.0a0,!=4.0.0a1->fsspec[http]<=2025.3.0,>=2023.1.0->datasets->-r /dgm/requirements.txt (line 1))
  Downloading propcache-0.3.1-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (10 kB)
Collecting yarl<2.0,>=1.17.0 (from aiohttp!=4.0.0a0,!=4.0.0a1->fsspec[http]<=2025.3.0,>=2023.1.0->datasets->-r /dgm/requirements.txt (line 1))
  Downloading yarl-1.20.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (72 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 72.4/72.4 kB 1.4 MB/s eta 0:00:00
Downloading datasets-3.6.0-py3-none-any.whl (491 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 491.5/491.5 kB 1.9 MB/s eta 0:00:00
Downloading anthropic-0.51.0-py3-none-any.whl (263 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 264.0/264.0 kB 362.0 kB/s eta 0:00:00
Downloading backoff-2.2.1-py3-none-any.whl (15 kB)
Downloading botocore-1.38.13-py3-none-any.whl (13.5 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 13.5/13.5 MB 8.4 MB/s eta 0:00:00
Downloading boto3-1.38.13-py3-none-any.whl (139 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 139.9/139.9 kB 316.9 kB/s eta 0:00:00
Downloading openai-1.78.0-py3-none-any.whl (680 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 680.4/680.4 kB 1.3 MB/s eta 0:00:00
Downloading beautifulsoup4-4.13.4-py3-none-any.whl (187 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 187.3/187.3 kB 260.1 kB/s eta 0:00:00
Downloading chardet-5.2.0-py3-none-any.whl (199 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 199.4/199.4 kB 532.9 kB/s eta 0:00:00
Downloading docker-7.1.0-py3-none-any.whl (147 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 147.8/147.8 kB 542.1 kB/s eta 0:00:00
Downloading ghapi-1.0.6-py3-none-any.whl (62 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 62.4/62.4 kB 851.2 kB/s eta 0:00:00
Downloading GitPython-3.1.44-py3-none-any.whl (207 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 207.6/207.6 kB 2.2 MB/s eta 0:00:00
Downloading pre_commit-4.2.0-py2.py3-none-any.whl (220 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 220.7/220.7 kB 1.7 MB/s eta 0:00:00
Downloading python_dotenv-1.1.0-py3-none-any.whl (20 kB)
Downloading rich-14.0.0-py3-none-any.whl (243 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 243.2/243.2 kB 5.7 MB/s eta 0:00:00
Downloading unidiff-0.7.5-py2.py3-none-any.whl (14 kB)
Using cached pytest-8.3.5-py3-none-any.whl (343 kB)
Downloading pytest_asyncio-0.26.0-py3-none-any.whl (19 kB)
Using cached async_timeout-5.0.1-py3-none-any.whl (6.2 kB)
Downloading anyio-4.9.0-py3-none-any.whl (100 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 100.9/100.9 kB 1.5 MB/s eta 0:00:00
Downloading cfgv-3.4.0-py2.py3-none-any.whl (7.2 kB)
Downloading dill-0.3.8-py3-none-any.whl (116 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 116.3/116.3 kB 1.8 MB/s eta 0:00:00
Downloading fastcore-1.8.2-py3-none-any.whl (78 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 78.2/78.2 kB 920.1 kB/s eta 0:00:00
Downloading fsspec-2025.3.0-py3-none-any.whl (193 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 193.6/193.6 kB 2.1 MB/s eta 0:00:00
Downloading gitdb-4.0.12-py3-none-any.whl (62 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 62.8/62.8 kB 671.1 kB/s eta 0:00:00
Downloading httpx-0.28.1-py3-none-any.whl (73 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 73.5/73.5 kB 1.1 MB/s eta 0:00:00
Downloading httpcore-1.0.9-py3-none-any.whl (78 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 78.8/78.8 kB 481.9 kB/s eta 0:00:00
Downloading huggingface_hub-0.31.1-py3-none-any.whl (484 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 484.3/484.3 kB 3.0 MB/s eta 0:00:00
Downloading identify-2.6.10-py2.py3-none-any.whl (99 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 99.1/99.1 kB 798.7 kB/s eta 0:00:00
Downloading jiter-0.9.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (351 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 351.8/351.8 kB 3.7 MB/s eta 0:00:00
Downloading jmespath-1.0.1-py3-none-any.whl (20 kB)
Downloading markdown_it_py-3.0.0-py3-none-any.whl (87 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 87.5/87.5 kB 831.6 kB/s eta 0:00:00
Downloading multiprocess-0.70.16-py311-none-any.whl (143 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 143.5/143.5 kB 754.7 kB/s eta 0:00:00
Downloading nodeenv-1.9.1-py2.py3-none-any.whl (22 kB)
Downloading numpy-2.2.5-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (16.4 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 16.4/16.4 MB 6.2 MB/s eta 0:00:00
Using cached pluggy-1.5.0-py3-none-any.whl (20 kB)
Downloading pyarrow-20.0.0-cp311-cp311-manylinux_2_28_x86_64.whl (42.3 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 42.3/42.3 MB 4.2 MB/s eta 0:00:00
Downloading pydantic-2.11.4-py3-none-any.whl (443 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 443.9/443.9 kB 3.0 MB/s eta 0:00:00
Downloading pydantic_core-2.33.2-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (2.0 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 2.0/2.0 MB 3.3 MB/s eta 0:00:00
Downloading pygments-2.19.1-py3-none-any.whl (1.2 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 1.2/1.2 MB 10.6 MB/s eta 0:00:00
Downloading python_dateutil-2.9.0.post0-py2.py3-none-any.whl (229 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 229.9/229.9 kB 990.0 kB/s eta 0:00:00
Downloading PyYAML-6.0.2-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (762 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 763.0/763.0 kB 3.8 MB/s eta 0:00:00
Downloading requests-2.32.3-py3-none-any.whl (64 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 64.9/64.9 kB 426.2 kB/s eta 0:00:00
Downloading s3transfer-0.12.0-py3-none-any.whl (84 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 84.8/84.8 kB 181.0 kB/s eta 0:00:00
Using cached sniffio-1.3.1-py3-none-any.whl (10 kB)
Downloading soupsieve-2.7-py3-none-any.whl (36 kB)
Downloading tqdm-4.67.1-py3-none-any.whl (78 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 78.5/78.5 kB 1.3 MB/s eta 0:00:00
Downloading typing_extensions-4.13.2-py3-none-any.whl (45 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 45.8/45.8 kB 835.0 kB/s eta 0:00:00
Downloading virtualenv-20.31.2-py3-none-any.whl (6.1 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 6.1/6.1 MB 13.7 MB/s eta 0:00:00
Downloading filelock-3.18.0-py3-none-any.whl (16 kB)
Using cached iniconfig-2.1.0-py3-none-any.whl (6.0 kB)
Downloading pandas-2.2.3-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (13.1 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 13.1/13.1 MB 24.4 MB/s eta 0:00:00
Downloading xxhash-3.5.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (194 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 194.8/194.8 kB 6.2 MB/s eta 0:00:00
Downloading aiohttp-3.11.18-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (1.7 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 1.7/1.7 MB 7.9 MB/s eta 0:00:00
Downloading annotated_types-0.7.0-py3-none-any.whl (13 kB)
Downloading distlib-0.3.9-py2.py3-none-any.whl (468 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 469.0/469.0 kB 9.2 MB/s eta 0:00:00
Downloading hf_xet-1.1.0-cp37-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (53.6 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 53.6/53.6 MB 2.8 MB/s eta 0:00:00
Downloading mdurl-0.1.2-py3-none-any.whl (10.0 kB)
Downloading pytz-2025.2-py2.py3-none-any.whl (509 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 509.2/509.2 kB 1.2 MB/s eta 0:00:00
Downloading six-1.17.0-py2.py3-none-any.whl (11 kB)
Downloading smmap-5.0.2-py3-none-any.whl (24 kB)
Downloading typing_inspection-0.4.0-py3-none-any.whl (14 kB)
Downloading tzdata-2025.2-py2.py3-none-any.whl (347 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 347.8/347.8 kB 2.8 MB/s eta 0:00:00
Downloading aiohappyeyeballs-2.6.1-py3-none-any.whl (15 kB)
Downloading aiosignal-1.3.2-py2.py3-none-any.whl (7.6 kB)
Using cached attrs-25.3.0-py3-none-any.whl (63 kB)
Downloading frozenlist-1.6.0-cp311-cp311-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl (313 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 313.6/313.6 kB 4.6 MB/s eta 0:00:00
Downloading h11-0.16.0-py3-none-any.whl (37 kB)
Downloading multidict-6.4.3-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (223 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 223.5/223.5 kB 1.5 MB/s eta 0:00:00
Downloading propcache-0.3.1-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (232 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 232.5/232.5 kB 2.7 MB/s eta 0:00:00
Downloading yarl-1.20.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (358 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 358.1/358.1 kB 11.6 MB/s eta 0:00:00
Installing collected packages: unidiff, pytz, distlib, xxhash, tzdata, typing-extensions, tqdm, soupsieve, sniffio, smmap, six, requests, pyyaml, python-dotenv, pygments, pyarrow, propcache, pluggy, numpy, nodeenv, multidict, mdurl, jmespath, jiter, iniconfig, identify, hf-xet, h11, fsspec, frozenlist, filelock, fastcore, dill, chardet, cfgv, backoff, attrs, async_timeout, annotated-types, aiohappyeyeballs, yarl, virtualenv, typing-inspection, python-dateutil, pytest, pydantic-core, multiprocess, markdown-it-py, huggingface-hub, httpcore, gitdb, ghapi, docker, beautifulsoup4, anyio, aiosignal, rich, pytest-asyncio, pydantic, pre-commit, pandas, httpx, GitPython, botocore, aiohttp, s3transfer, openai, anthropic, datasets, boto3
  Attempting uninstall: tqdm
    Found existing installation: tqdm 4.65.0
    Uninstalling tqdm-4.65.0:
      Successfully uninstalled tqdm-4.65.0
  Attempting uninstall: requests
    Found existing installation: requests 2.31.0
    Uninstalling requests-2.31.0:
      Successfully uninstalled requests-2.31.0
  Attempting uninstall: pluggy
    Found existing installation: pluggy 1.0.0
    Uninstalling pluggy-1.0.0:
      Successfully uninstalled pluggy-1.0.0
WARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv
Successfully installed GitPython-3.1.44 aiohappyeyeballs-2.6.1 aiohttp-3.11.18 aiosignal-1.3.2 annotated-types-0.7.0 anthropic-0.51.0 anyio-4.9.0 async_timeout-5.0.1 attrs-25.3.0 backoff-2.2.1 beautifulsoup4-4.13.4 boto3-1.38.13 botocore-1.38.13 cfgv-3.4.0 chardet-5.2.0 datasets-3.6.0 dill-0.3.8 distlib-0.3.9 docker-7.1.0 fastcore-1.8.2 filelock-3.18.0 frozenlist-1.6.0 fsspec-2025.3.0 ghapi-1.0.6 gitdb-4.0.12 h11-0.16.0 hf-xet-1.1.0 httpcore-1.0.9 httpx-0.28.1 huggingface-hub-0.31.1 identify-2.6.10 iniconfig-2.1.0 jiter-0.9.0 jmespath-1.0.1 markdown-it-py-3.0.0 mdurl-0.1.2 multidict-6.4.3 multiprocess-0.70.16 nodeenv-1.9.1 numpy-2.2.5 openai-1.78.0 pandas-2.2.3 pluggy-1.5.0 pre-commit-4.2.0 propcache-0.3.1 pyarrow-20.0.0 pydantic-2.11.4 pydantic-core-2.33.2 pygments-2.19.1 pytest-8.3.5 pytest-asyncio-0.26.0 python-dateutil-2.9.0.post0 python-dotenv-1.1.0 pytz-2025.2 pyyaml-6.0.2 requests-2.32.3 rich-14.0.0 s3transfer-0.12.0 six-1.17.0 smmap-5.0.2 sniffio-1.3.1 soupsieve-2.7 tqdm-4.67.1 typing-extensions-4.13.2 typing-inspection-0.4.0 tzdata-2025.2 unidiff-0.7.5 virtualenv-20.31.2 xxhash-3.5.0 yarl-1.20.0

2025-05-10 02:43:49,768 - ThreadPoolExecutor-0_17 - INFO - Running the agent
2025-05-10 02:44:27,768 - ThreadPoolExecutor-0_17 - INFO - Container output: Using OpenAI API with model o3-mini-2025-01-31.

2025-05-10 02:44:27,776 - ThreadPoolExecutor-0_17 - INFO - Copying output files back to host
2025-05-10 02:44:28,329 - ThreadPoolExecutor-0_17 - INFO - Copying from container /dgm/python__paasio.md to local path polyglot/predictions/20250510_021328--claude-3-5-sonnet-20241022_0/python__paasio.md
2025-05-10 02:44:28,389 - ThreadPoolExecutor-0_17 - INFO - Successfully copied from container to polyglot/predictions/20250510_021328--claude-3-5-sonnet-20241022_0/python__paasio.md
2025-05-10 02:44:28,608 - ThreadPoolExecutor-0_17 - INFO - Getting model_patch
2025-05-10 02:44:28,888 - ThreadPoolExecutor-0_17 - INFO - Container output: diff --git a/paasio.py b/paasio.py
index 62987c6..4977d82 100644
--- a/paasio.py
+++ b/paasio.py
@@ -2,76 +2,118 @@ import io
 
 
 class MeteredFile(io.BufferedRandom):
-    """Implement using a subclassing model."""
+    """A metered file that tracks read/write statistics.
+
+    Attributes:
+        read_bytes: total number of bytes read.
+        read_ops: total read operations performed.
+        write_bytes: total number of bytes written.
+        write_ops: total write operations performed.
+    """
 
     def __init__(self, *args, **kwargs):
-        pass
+        # Initialize the underlying BufferedRandom
+        super().__init__(*args, **kwargs)
+        self._read_bytes = 0
+        self._read_ops = 0
+        self._write_bytes = 0
+        self._write_ops = 0
 
     def __enter__(self):
-        pass
+        return self
 
     def __exit__(self, exc_type, exc_val, exc_tb):
-        pass
+        self.close()
 
     def __iter__(self):
-        pass
+        return self
 
     def __next__(self):
-        pass
+        line = self.readline()
+        if not line:
+            raise StopIteration
+        return line
 
     def read(self, size=-1):
-        pass
+        data = super().read(size)
+        self._read_ops += 1
+        self._read_bytes += len(data) if data is not None else 0
+        return data
 
     @property
     def read_bytes(self):
-        pass
+        return self._read_bytes
 
     @property
     def read_ops(self):
-        pass
+        return self._read_ops
 
     def write(self, b):
-        pass
+        num = super().write(b)
+        # If write returns None, not sure of number of bytes, so assume len(b) written.
+        written = num if num is not None else len(b)
+        self._write_ops += 1
+        self._write_bytes += written
+        return num
 
     @property
     def write_bytes(self):
-        pass
+        return self._write_bytes
 
     @property
     def write_ops(self):
-        pass
+        return self._write_ops
 
 
 class MeteredSocket:
-    """Implement using a delegation model."""
+    """A metered socket that tracks network IO statistics.
+
+    Attributes:
+        recv_bytes: total number of bytes received.
+        recv_ops: total recv operations performed.
+        send_bytes: total number of bytes sent.
+        send_ops: total send operations performed.
+    """
 
     def __init__(self, socket):
-        pass
+        self._socket = socket
+        self._recv_bytes = 0
+        self._recv_ops = 0
+        self._send_bytes = 0
+        self._send_ops = 0
 
     def __enter__(self):
-        pass
+        return self
 
     def __exit__(self, exc_type, exc_val, exc_tb):
-        pass
+        self._socket.close()
 
     def recv(self, bufsize, flags=0):
-        pass
+        data = self._socket.recv(bufsize, flags)
+        self._recv_ops += 1
+        self._recv_bytes += len(data)
+        return data
 
     @property
     def recv_bytes(self):
-        pass
+        return self._recv_bytes
 
     @property
     def recv_ops(self):
-        pass
+        return self._recv_ops
 
     def send(self, data, flags=0):
-        pass
+        num = self._socket.send(data, flags)
+        # In case send returns None, assume full length
+        sent = num if num is not None else len(data)
+        self._send_ops += 1
+        self._send_bytes += sent
+        return num
 
     @property
     def send_bytes(self):
-        pass
+        return self._send_bytes
 
     @property
     def send_ops(self):
-        pass
+        return self._send_ops

2025-05-10 02:44:29,739 - ThreadPoolExecutor-0_17 - INFO - Container output: Saved working directory and index state WIP on master: 3a8f72b Initial commit

2025-05-10 02:44:30,271 - ThreadPoolExecutor-0_17 - INFO - Container output: HEAD is now at 4dd032b all files

2025-05-10 02:44:30,437 - ThreadPoolExecutor-0_17 - INFO - Container output: 
2025-05-10 02:44:31,442 - ThreadPoolExecutor-0_17 - INFO - Container output: On branch master
Your branch is up to date with 'origin/master'.

Changes not staged for commit:
  (use "git add <file>..." to update what will be committed)
  (use "git restore <file>..." to discard changes in working directory)
	modified:   paasio.py

no changes added to commit (use "git add" and/or "git commit -a")
Dropped refs/stash@{0} (2abd3be124f4c71007e7d346dbd7868b867e74bc)

2025-05-10 02:44:31,443 - ThreadPoolExecutor-0_17 - INFO - Running the eval
2025-05-10 02:44:31,988 - ThreadPoolExecutor-0_17 - INFO - Copying polyglot/predictions/20250510_021328--claude-3-5-sonnet-20241022_0/python__paasio_eval.sh to container at /testbed/eval.sh
2025-05-10 02:44:32,009 - ThreadPoolExecutor-0_17 - INFO - Successfully copied polyglot/predictions/20250510_021328--claude-3-5-sonnet-20241022_0/python__paasio_eval.sh to container
2025-05-10 02:44:32,968 - ThreadPoolExecutor-0_17 - INFO - Container output: /testbed:
eval.sh
paasio.py
paasio_test.py
test_utils.py

2025-05-10 02:44:33,541 - ThreadPoolExecutor-0_17 - INFO - Container output: 
2025-05-10 02:44:45,265 - ThreadPoolExecutor-0_17 - INFO - Container output: + pytest -rA --tb=long
/opt/miniconda3/lib/python3.11/site-packages/pytest_asyncio/plugin.py:217: PytestDeprecationWarning: The configuration option "asyncio_default_fixture_loop_scope" is unset.
The event loop scope for asynchronous fixtures will default to the fixture caching scope. Future versions of pytest-asyncio will default the loop scope for asynchronous fixtures to function scope. Set the default fixture loop scope explicitly in order to avoid unexpected behavior in the future. Valid fixture loop scopes are: "function", "class", "module", "package", "session"

  warnings.warn(PytestDeprecationWarning(_DEFAULT_FIXTURE_LOOP_SCOPE_UNSET))
============================= test session starts ==============================
platform linux -- Python 3.11.5, pytest-8.3.5, pluggy-1.5.0
rootdir: /testbed
plugins: asyncio-0.26.0, anyio-4.9.0
asyncio: mode=Mode.STRICT, asyncio_default_fixture_loop_scope=None, asyncio_default_test_loop_scope=function
collected 25 items

paasio_test.py FFFFFFFFFFFFFFFFFFFFFFFFF                                 [100%]

=================================== FAILURES ===================================
_________________ PaasioTest.test_meteredfile_context_manager __________________

self = <paasio_test.PaasioTest testMethod=test_meteredfile_context_manager>
super_mock = <SuperMock at 0x7c062cf57790 with mock object: <NonCallableMagicMock id='136365957003856'>>

    @patch("paasio.super", create=True, new_callable=SuperMock)
    def test_meteredfile_context_manager(self, super_mock):
        wrapped = MockFile(ZEN)
        mock = NonCallableMagicMock(wraps=wrapped, autospec=True)
        mock.__exit__.side_effect = wrapped.__exit__
        super_mock.mock_object = mock
>       with MeteredFile() as file:

paasio_test.py:221: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <MeteredFile>, exc_type = None, exc_val = None, exc_tb = None

    def __exit__(self, exc_type, exc_val, exc_tb):
>       self.close()
E       ValueError: I/O operation on uninitialized object

paasio.py:26: ValueError
_________ PaasioTest.test_meteredfile_context_manager_exception_raise __________

self = <paasio_test.PaasioTest testMethod=test_meteredfile_context_manager_exception_raise>
super_mock = <SuperMock at 0x7c062c61d010 with mock object: <NonCallableMagicMock id='136365956261520'>>

    @patch("paasio.super", create=True, new_callable=SuperMock)
    def test_meteredfile_context_manager_exception_raise(self, super_mock):
        exception = MockException("Should raise")
        wrapped = MockFile(ZEN, exception=exception)
        mock = NonCallableMagicMock(wraps=wrapped, autospec=True)
        mock.__exit__.side_effect = wrapped.__exit__
        super_mock.mock_object = mock
        with self.assertRaisesRegex(MockException, "Should raise") as err:
            with MeteredFile() as file:
                self.assertFalse(mock.__enter__.called)
>               file.read()

paasio_test.py:243: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <MeteredFile>, size = -1

    def read(self, size=-1):
>       data = super().read(size)

paasio.py:38: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <MagicMock name='mock.read' id='136365952758096'>, args = (-1,)
kwargs = {}

    def __call__(self, /, *args, **kwargs):
        # can't use self in-case a function / method we are mocking uses self
        # in the signature
        self._mock_check_sig(*args, **kwargs)
        self._increment_mock_call(*args, **kwargs)
>       return self._mock_call(*args, **kwargs)

/opt/miniconda3/lib/python3.11/unittest/mock.py:1124: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <MagicMock name='mock.read' id='136365952758096'>, args = (-1,)
kwargs = {}

    def _mock_call(self, /, *args, **kwargs):
>       return self._execute_mock_call(*args, **kwargs)

/opt/miniconda3/lib/python3.11/unittest/mock.py:1128: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <MagicMock name='mock.read' id='136365952758096'>, args = (-1,)
kwargs = {}, effect = None

    def _execute_mock_call(self, /, *args, **kwargs):
        # separate from _increment_mock_call so that awaited functions are
        # executed separately from their call, also AsyncMock overrides this method
    
        effect = self.side_effect
        if effect is not None:
            if _is_exception(effect):
                raise effect
            elif not _callable(effect):
                result = next(effect)
                if _is_exception(result):
                    raise result
            else:
                result = effect(*args, **kwargs)
    
            if result is not DEFAULT:
                return result
    
        if self._mock_return_value is not DEFAULT:
            return self.return_value
    
        if self._mock_wraps is not None:
>           return self._mock_wraps(*args, **kwargs)

/opt/miniconda3/lib/python3.11/unittest/mock.py:1198: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <test_utils.MockFile object at 0x7c062c5f9b20>, size = -1

    def read(self, size=-1):
        if self.__exception is not None:
>           raise self.__exception
E           test_utils.MockException: Should raise

test_utils.py:47: MockException

During handling of the above exception, another exception occurred:

self = <paasio_test.PaasioTest testMethod=test_meteredfile_context_manager_exception_raise>
super_mock = <SuperMock at 0x7c062c61d010 with mock object: <NonCallableMagicMock id='136365956261520'>>

    @patch("paasio.super", create=True, new_callable=SuperMock)
    def test_meteredfile_context_manager_exception_raise(self, super_mock):
        exception = MockException("Should raise")
        wrapped = MockFile(ZEN, exception=exception)
        mock = NonCallableMagicMock(wraps=wrapped, autospec=True)
        mock.__exit__.side_effect = wrapped.__exit__
        super_mock.mock_object = mock
        with self.assertRaisesRegex(MockException, "Should raise") as err:
>           with MeteredFile() as file:

paasio_test.py:241: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

    def __exit__(self, exc_type, exc_val, exc_tb):
>       self.close()
E       ValueError: I/O operation on uninitialized object

paasio.py:26: ValueError
________ PaasioTest.test_meteredfile_context_manager_exception_suppress ________

self = <paasio_test.PaasioTest testMethod=test_meteredfile_context_manager_exception_suppress>
super_mock = <SuperMock at 0x7c062c3284d0 with mock object: <NonCallableMagicMock id='136365953157520'>>

    @patch("paasio.super", create=True, new_callable=SuperMock)
    def test_meteredfile_context_manager_exception_suppress(self, super_mock):
        exception = MockException("Should suppress")
        wrapped = MockFile(ZEN, exception=exception)
        mock = NonCallableMagicMock(wraps=wrapped, autospec=True)
        mock.__exit__.side_effect = wrapped.__exit__
        super_mock.mock_object = mock
        with MeteredFile() as file:
            self.assertFalse(mock.__enter__.called)
>           file.read()

paasio_test.py:261: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <MeteredFile>, size = -1

    def read(self, size=-1):
>       data = super().read(size)

paasio.py:38: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <MagicMock name='mock.read' id='136365952810896'>, args = (-1,)
kwargs = {}

    def __call__(self, /, *args, **kwargs):
        # can't use self in-case a function / method we are mocking uses self
        # in the signature
        self._mock_check_sig(*args, **kwargs)
        self._increment_mock_call(*args, **kwargs)
>       return self._mock_call(*args, **kwargs)

/opt/miniconda3/lib/python3.11/unittest/mock.py:1124: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <MagicMock name='mock.read' id='136365952810896'>, args = (-1,)
kwargs = {}

    def _mock_call(self, /, *args, **kwargs):
>       return self._execute_mock_call(*args, **kwargs)

/opt/miniconda3/lib/python3.11/unittest/mock.py:1128: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <MagicMock name='mock.read' id='136365952810896'>, args = (-1,)
kwargs = {}, effect = None

    def _execute_mock_call(self, /, *args, **kwargs):
        # separate from _increment_mock_call so that awaited functions are
        # executed separately from their call, also AsyncMock overrides this method
    
        effect = self.side_effect
        if effect is not None:
            if _is_exception(effect):
                raise effect
            elif not _callable(effect):
                result = next(effect)
                if _is_exception(result):
                    raise result
            else:
                result = effect(*args, **kwargs)
    
            if result is not DEFAULT:
                return result
    
        if self._mock_return_value is not DEFAULT:
            return self.return_value
    
        if self._mock_wraps is not None:
>           return self._mock_wraps(*args, **kwargs)

/opt/miniconda3/lib/python3.11/unittest/mock.py:1198: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <test_utils.MockFile object at 0x7c062c3ccdb0>, size = -1

    def read(self, size=-1):
        if self.__exception is not None:
>           raise self.__exception
E           test_utils.MockException: Should suppress

test_utils.py:47: MockException

During handling of the above exception, another exception occurred:

self = <paasio_test.PaasioTest testMethod=test_meteredfile_context_manager_exception_suppress>
super_mock = <SuperMock at 0x7c062c3284d0 with mock object: <NonCallableMagicMock id='136365953157520'>>

    @patch("paasio.super", create=True, new_callable=SuperMock)
    def test_meteredfile_context_manager_exception_suppress(self, super_mock):
        exception = MockException("Should suppress")
        wrapped = MockFile(ZEN, exception=exception)
        mock = NonCallableMagicMock(wraps=wrapped, autospec=True)
        mock.__exit__.side_effect = wrapped.__exit__
        super_mock.mock_object = mock
>       with MeteredFile() as file:

paasio_test.py:259: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <MeteredFile>, exc_type = <class 'test_utils.MockException'>
exc_val = MockException('Should suppress')
exc_tb = <traceback object at 0x7c062c2d1200>

    def __exit__(self, exc_type, exc_val, exc_tb):
>       self.close()
E       ValueError: I/O operation on uninitialized object

paasio.py:26: ValueError
____________________ PaasioTest.test_meteredfile_iteration _____________________

self = <paasio_test.PaasioTest testMethod=test_meteredfile_iteration>
super_mock = <SuperMock at 0x7c062c34d490 with mock object: <NonCallableMagicMock id='136365953308560'>>

    @patch("paasio.super", create=True, new_callable=SuperMock)
    def test_meteredfile_iteration(self, super_mock):
        mock = NonCallableMagicMock(wraps=MockFile(ZEN), autospec=True)
        super_mock.mock_object = mock
        actual_reads = b""
        file = MeteredFile()
>       for line in file:

paasio_test.py:275: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <MeteredFile>

    def __next__(self):
>       line = self.readline()
E       ValueError: I/O operation on uninitialized object

paasio.py:32: ValueError
__________________ PaasioTest.test_meteredfile_read_multiple ___________________

self = <paasio_test.PaasioTest testMethod=test_meteredfile_read_multiple>
super_mock = <SuperMock at 0x7c062c3f6910 with mock object: <NonCallableMagicMock id='136365954001232'>>

    @patch("paasio.super", create=True, new_callable=SuperMock)
    def test_meteredfile_read_multiple(self, super_mock):
        wrapped = MockFile(ZEN)
        mock = NonCallableMagicMock(wraps=wrapped, autospec=True)
        super_mock.mock_object = mock
        actual_read = b""
>       with MeteredFile() as file:

paasio_test.py:319: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <MeteredFile>, exc_type = None, exc_val = None, exc_tb = None

    def __exit__(self, exc_type, exc_val, exc_tb):
>       self.close()
E       ValueError: I/O operation on uninitialized object

paasio.py:26: ValueError
_______________ PaasioTest.test_meteredfile_read_multiple_chunk ________________

self = <paasio_test.PaasioTest testMethod=test_meteredfile_read_multiple_chunk>
super_mock = <SuperMock at 0x7c062c2fedd0 with mock object: <NonCallableMagicMock id='136365952983184'>>

    @patch("paasio.super", create=True, new_callable=SuperMock)
    def test_meteredfile_read_multiple_chunk(self, super_mock):
        wrapped = MockFile(ZEN, chunk=20)
        mock = NonCallableMagicMock(wraps=wrapped, autospec=True)
        super_mock.mock_object = mock
        actual_read = b""
>       with MeteredFile() as file:

paasio_test.py:333: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <MeteredFile>, exc_type = None, exc_val = None, exc_tb = None

    def __exit__(self, exc_type, exc_val, exc_tb):
>       self.close()
E       ValueError: I/O operation on uninitialized object

paasio.py:26: ValueError
____________________ PaasioTest.test_meteredfile_read_once _____________________

self = <paasio_test.PaasioTest testMethod=test_meteredfile_read_once>
super_mock = <SuperMock at 0x7c062c300850 with mock object: <NonCallableMagicMock id='136365953000784'>>

    @patch("paasio.super", create=True, new_callable=SuperMock)
    def test_meteredfile_read_once(self, super_mock):
        mock = NonCallableMagicMock(wraps=MockFile(ZEN), autospec=True)
        super_mock.mock_object = mock
>       with MeteredFile() as file:

paasio_test.py:290: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <MeteredFile>, exc_type = None, exc_val = None, exc_tb = None

    def __exit__(self, exc_type, exc_val, exc_tb):
>       self.close()
E       ValueError: I/O operation on uninitialized object

paasio.py:26: ValueError
_________________ PaasioTest.test_meteredfile_read_under_size __________________

self = <paasio_test.PaasioTest testMethod=test_meteredfile_read_under_size>
super_mock = <SuperMock at 0x7c062c3022d0 with mock object: <NonCallableMagicMock id='136365953004560'>>

    @patch("paasio.super", create=True, new_callable=SuperMock)
    def test_meteredfile_read_under_size(self, super_mock):
        wrapped = MockFile(ZEN, chunk=257)  # largish odd number
        mock = NonCallableMagicMock(wraps=wrapped, autospec=True)
        super_mock.mock_object = mock
>       with MeteredFile() as file:

paasio_test.py:359: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <MeteredFile>, exc_type = None, exc_val = None, exc_tb = None

    def __exit__(self, exc_type, exc_val, exc_tb):
>       self.close()
E       ValueError: I/O operation on uninitialized object

paasio.py:26: ValueError
_________________ PaasioTest.test_meteredfile_stats_read_only __________________

self = <paasio_test.PaasioTest testMethod=test_meteredfile_stats_read_only>
super_mock = <SuperMock at 0x7c062c2e4590 with mock object: <NonCallableMagicMock id='136365952877776'>>

    @patch("paasio.super", create=True, new_callable=SuperMock)
    def test_meteredfile_stats_read_only(self, super_mock):
        mock = NonCallableMagicMock(wraps=MockFile(ZEN), autospec=True)
        super_mock.mock_object = mock
>       with MeteredFile() as file:

paasio_test.py:414: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <MeteredFile>, exc_type = None, exc_val = None, exc_tb = None

    def __exit__(self, exc_type, exc_val, exc_tb):
>       self.close()
E       ValueError: I/O operation on uninitialized object

paasio.py:26: ValueError
__________________ PaasioTest.test_meteredfile_write_multiple __________________

self = <paasio_test.PaasioTest testMethod=test_meteredfile_write_multiple>
super_mock = <SuperMock at 0x7c062c2fc250 with mock object: <NonCallableMagicMock id='136365952989840'>>

    @patch("paasio.super", create=True, new_callable=SuperMock)
    def test_meteredfile_write_multiple(self, super_mock):
        wrapped = MockFile()
        mock = NonCallableMagicMock(wraps=wrapped, autospec=True)
        super_mock.mock_object = mock
        write_len = 0
        expected = b"Tomorrow's victory is today's practice."
>       with MeteredFile() as file:

paasio_test.py:386: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <MeteredFile>, exc_type = None, exc_val = None, exc_tb = None

    def __exit__(self, exc_type, exc_val, exc_tb):
>       self.close()
E       ValueError: I/O operation on uninitialized object

paasio.py:26: ValueError
____________________ PaasioTest.test_meteredfile_write_once ____________________

self = <paasio_test.PaasioTest testMethod=test_meteredfile_write_once>
super_mock = <SuperMock at 0x7c062c599150 with mock object: <NonCallableMagicMock id='136365955714896'>>

    @patch("paasio.super", create=True, new_callable=SuperMock)
    def test_meteredfile_write_once(self, super_mock):
        wrapped = MockFile(chunk=257)  # largish odd number
        mock = NonCallableMagicMock(wraps=wrapped, autospec=True)
        super_mock.mock_object = mock
>       with MeteredFile() as file:

paasio_test.py:371: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <MeteredFile>, exc_type = None, exc_val = None, exc_tb = None

    def __exit__(self, exc_type, exc_val, exc_tb):
>       self.close()
E       ValueError: I/O operation on uninitialized object

paasio.py:26: ValueError
_________________ PaasioTest.test_meteredfile_write_under_size _________________

self = <paasio_test.PaasioTest testMethod=test_meteredfile_write_under_size>
super_mock = <SuperMock at 0x7c062c2f3bd0 with mock object: <NonCallableMagicMock id='136365952940624'>>

    @patch("paasio.super", create=True, new_callable=SuperMock)
    def test_meteredfile_write_under_size(self, super_mock):
        wrapped = MockFile(chunk=257)  # largish odd number
        mock = NonCallableMagicMock(wraps=wrapped, autospec=True)
        super_mock.mock_object = mock
>       with MeteredFile() as file:

paasio_test.py:402: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <MeteredFile>, exc_type = None, exc_val = None, exc_tb = None

    def __exit__(self, exc_type, exc_val, exc_tb):
>       self.close()
E       ValueError: I/O operation on uninitialized object

paasio.py:26: ValueError
________________ PaasioTest.test_meteredsocket_bufsize_required ________________

self = <paasio_test.PaasioTest testMethod=test_meteredsocket_bufsize_required>

    def test_meteredsocket_bufsize_required(self):
        mock = NonCallableMagicMock(wraps=MockSock(), autospec=True)
        with self.assertRaisesRegex(TypeError, "argument"):
            with MeteredSocket(mock) as socket:
>               socket.recv()
E               TypeError: MeteredSocket.recv() missing 1 required positional argument: 'bufsize'

paasio_test.py:145: TypeError

During handling of the above exception, another exception occurred:

self = <paasio_test.PaasioTest testMethod=test_meteredsocket_bufsize_required>

    def test_meteredsocket_bufsize_required(self):
        mock = NonCallableMagicMock(wraps=MockSock(), autospec=True)
        with self.assertRaisesRegex(TypeError, "argument"):
>           with MeteredSocket(mock) as socket:

paasio_test.py:144: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

    def __exit__(self, exc_type, exc_val, exc_tb):
>       self._socket.close()

paasio.py:89: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

    def __getattr__(self, name):
        if name in {'_mock_methods', '_mock_unsafe'}:
            raise AttributeError(name)
        elif self._mock_methods is not None:
            if name not in self._mock_methods or name in _all_magics:
                raise AttributeError("Mock object has no attribute %r" % name)
        elif _is_magic(name):
            raise AttributeError(name)
        if not self._mock_unsafe and (not self._mock_methods or name not in self._mock_methods):
            if name.startswith(('assert', 'assret', 'asert', 'aseert', 'assrt')):
                raise AttributeError(
                    f"{name!r} is not a valid assertion. Use a spec "
                    f"for the mock if {name!r} is meant to be an attribute.")
    
        with NonCallableMock._lock:
            result = self._mock_children.get(name)
            if result is _deleted:
                raise AttributeError(name)
            elif result is None:
                wraps = None
                if self._mock_wraps is not None:
                    # XXXX should we get the attribute without triggering code
                    # execution?
>                   wraps = getattr(self._mock_wraps, name)
E                   AttributeError: 'MockSock' object has no attribute 'close'

/opt/miniconda3/lib/python3.11/unittest/mock.py:671: AttributeError
________________ PaasioTest.test_meteredsocket_context_manager _________________

self = <paasio_test.PaasioTest testMethod=test_meteredsocket_context_manager>

    def test_meteredsocket_context_manager(self):
        wrapped = MockSock()
        mock = NonCallableMagicMock(wraps=wrapped, autospec=True)
        mock.__exit__.side_effect = wrapped.__exit__
>       with MeteredSocket(mock) as socket:

paasio_test.py:16: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <paasio.MeteredSocket object at 0x7c062c6694d0>, exc_type = None
exc_val = None, exc_tb = None

    def __exit__(self, exc_type, exc_val, exc_tb):
>       self._socket.close()

paasio.py:89: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <NonCallableMagicMock id='136365957106384'>, name = 'close'

    def __getattr__(self, name):
        if name in {'_mock_methods', '_mock_unsafe'}:
            raise AttributeError(name)
        elif self._mock_methods is not None:
            if name not in self._mock_methods or name in _all_magics:
                raise AttributeError("Mock object has no attribute %r" % name)
        elif _is_magic(name):
            raise AttributeError(name)
        if not self._mock_unsafe and (not self._mock_methods or name not in self._mock_methods):
            if name.startswith(('assert', 'assret', 'asert', 'aseert', 'assrt')):
                raise AttributeError(
                    f"{name!r} is not a valid assertion. Use a spec "
                    f"for the mock if {name!r} is meant to be an attribute.")
    
        with NonCallableMock._lock:
            result = self._mock_children.get(name)
            if result is _deleted:
                raise AttributeError(name)
            elif result is None:
                wraps = None
                if self._mock_wraps is not None:
                    # XXXX should we get the attribute without triggering code
                    # execution?
>                   wraps = getattr(self._mock_wraps, name)
E                   AttributeError: 'MockSock' object has no attribute 'close'

/opt/miniconda3/lib/python3.11/unittest/mock.py:671: AttributeError
________ PaasioTest.test_meteredsocket_context_manager_exception_raise _________

self = <paasio_test.PaasioTest testMethod=test_meteredsocket_context_manager_exception_raise>

    def test_meteredsocket_context_manager_exception_raise(self):
        exception = MockException("Should raise")
        wrapped = MockSock(exception=exception)
        mock = NonCallableMagicMock(wraps=wrapped, autospec=True)
        mock.__exit__.side_effect = wrapped.__exit__
        with self.assertRaisesRegex(MockException, "Should raise") as err:
            with MeteredSocket(mock) as socket:
                self.assertFalse(mock.__enter__.called)
>               socket.recv(4096)

paasio_test.py:35: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <paasio.MeteredSocket object at 0x7c062c2c3dd0>, bufsize = 4096
flags = 0

    def recv(self, bufsize, flags=0):
>       data = self._socket.recv(bufsize, flags)

paasio.py:92: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <MagicMock name='mock.recv' id='136365965330128'>, args = (4096, 0)
kwargs = {}

    def __call__(self, /, *args, **kwargs):
        # can't use self in-case a function / method we are mocking uses self
        # in the signature
        self._mock_check_sig(*args, **kwargs)
        self._increment_mock_call(*args, **kwargs)
>       return self._mock_call(*args, **kwargs)

/opt/miniconda3/lib/python3.11/unittest/mock.py:1124: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <MagicMock name='mock.recv' id='136365965330128'>, args = (4096, 0)
kwargs = {}

    def _mock_call(self, /, *args, **kwargs):
>       return self._execute_mock_call(*args, **kwargs)

/opt/miniconda3/lib/python3.11/unittest/mock.py:1128: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <MagicMock name='mock.recv' id='136365965330128'>, args = (4096, 0)
kwargs = {}, effect = None

    def _execute_mock_call(self, /, *args, **kwargs):
        # separate from _increment_mock_call so that awaited functions are
        # executed separately from their call, also AsyncMock overrides this method
    
        effect = self.side_effect
        if effect is not None:
            if _is_exception(effect):
                raise effect
            elif not _callable(effect):
                result = next(effect)
                if _is_exception(result):
                    raise result
            else:
                result = effect(*args, **kwargs)
    
            if result is not DEFAULT:
                return result
    
        if self._mock_return_value is not DEFAULT:
            return self.return_value
    
        if self._mock_wraps is not None:
>           return self._mock_wraps(*args, **kwargs)

/opt/miniconda3/lib/python3.11/unittest/mock.py:1198: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <test_utils.MockSock object at 0x7c062c2c2450>, bufsize = 4096, flags = 0

    def recv(self, bufsize, flags=0):
        if self.__closed:
            raise OSError(errno.EBADF, os.strerror(errno.EBADF))
        if bufsize is None:
            raise TypeError("'NoneType' object cannot be interpreted as an integer")
        if not isinstance(flags, int):
            raise TypeError(
                "an integer is required (got type {})".format(type(flags).__name__)
            )
        self.flags = flags
        if self.__exception is not None:
>           raise self.__exception
E           test_utils.MockException: Should raise

test_utils.py:93: MockException

During handling of the above exception, another exception occurred:

self = <paasio_test.PaasioTest testMethod=test_meteredsocket_context_manager_exception_raise>

    def test_meteredsocket_context_manager_exception_raise(self):
        exception = MockException("Should raise")
        wrapped = MockSock(exception=exception)
        mock = NonCallableMagicMock(wraps=wrapped, autospec=True)
        mock.__exit__.side_effect = wrapped.__exit__
        with self.assertRaisesRegex(MockException, "Should raise") as err:
>           with MeteredSocket(mock) as socket:

paasio_test.py:33: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

    def __exit__(self, exc_type, exc_val, exc_tb):
>       self._socket.close()

paasio.py:89: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

    def __getattr__(self, name):
        if name in {'_mock_methods', '_mock_unsafe'}:
            raise AttributeError(name)
        elif self._mock_methods is not None:
            if name not in self._mock_methods or name in _all_magics:
                raise AttributeError("Mock object has no attribute %r" % name)
        elif _is_magic(name):
            raise AttributeError(name)
        if not self._mock_unsafe and (not self._mock_methods or name not in self._mock_methods):
            if name.startswith(('assert', 'assret', 'asert', 'aseert', 'assrt')):
                raise AttributeError(
                    f"{name!r} is not a valid assertion. Use a spec "
                    f"for the mock if {name!r} is meant to be an attribute.")
    
        with NonCallableMock._lock:
            result = self._mock_children.get(name)
            if result is _deleted:
                raise AttributeError(name)
            elif result is None:
                wraps = None
                if self._mock_wraps is not None:
                    # XXXX should we get the attribute without triggering code
                    # execution?
>                   wraps = getattr(self._mock_wraps, name)
E                   AttributeError: 'MockSock' object has no attribute 'close'

/opt/miniconda3/lib/python3.11/unittest/mock.py:671: AttributeError
_______ PaasioTest.test_meteredsocket_context_manager_exception_suppress _______

self = <paasio_test.PaasioTest testMethod=test_meteredsocket_context_manager_exception_suppress>

    def test_meteredsocket_context_manager_exception_suppress(self):
        exception = MockException("Should suppress")
        wrapped = MockSock(exception=exception)
        mock = NonCallableMagicMock(wraps=wrapped, autospec=True)
        mock.__exit__.side_effect = wrapped.__exit__
        with MeteredSocket(mock) as socket:
            self.assertFalse(mock.__enter__.called)
>           socket.recv(4096)

paasio_test.py:51: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <paasio.MeteredSocket object at 0x7c062cf6e390>, bufsize = 4096
flags = 0

    def recv(self, bufsize, flags=0):
>       data = self._socket.recv(bufsize, flags)

paasio.py:92: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <MagicMock name='mock.recv' id='136365953384080'>, args = (4096, 0)
kwargs = {}

    def __call__(self, /, *args, **kwargs):
        # can't use self in-case a function / method we are mocking uses self
        # in the signature
        self._mock_check_sig(*args, **kwargs)
        self._increment_mock_call(*args, **kwargs)
>       return self._mock_call(*args, **kwargs)

/opt/miniconda3/lib/python3.11/unittest/mock.py:1124: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <MagicMock name='mock.recv' id='136365953384080'>, args = (4096, 0)
kwargs = {}

    def _mock_call(self, /, *args, **kwargs):
>       return self._execute_mock_call(*args, **kwargs)

/opt/miniconda3/lib/python3.11/unittest/mock.py:1128: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <MagicMock name='mock.recv' id='136365953384080'>, args = (4096, 0)
kwargs = {}, effect = None

    def _execute_mock_call(self, /, *args, **kwargs):
        # separate from _increment_mock_call so that awaited functions are
        # executed separately from their call, also AsyncMock overrides this method
    
        effect = self.side_effect
        if effect is not None:
            if _is_exception(effect):
                raise effect
            elif not _callable(effect):
                result = next(effect)
                if _is_exception(result):
                    raise result
            else:
                result = effect(*args, **kwargs)
    
            if result is not DEFAULT:
                return result
    
        if self._mock_return_value is not DEFAULT:
            return self.return_value
    
        if self._mock_wraps is not None:
>           return self._mock_wraps(*args, **kwargs)

/opt/miniconda3/lib/python3.11/unittest/mock.py:1198: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <test_utils.MockSock object at 0x7c062c34f550>, bufsize = 4096, flags = 0

    def recv(self, bufsize, flags=0):
        if self.__closed:
            raise OSError(errno.EBADF, os.strerror(errno.EBADF))
        if bufsize is None:
            raise TypeError("'NoneType' object cannot be interpreted as an integer")
        if not isinstance(flags, int):
            raise TypeError(
                "an integer is required (got type {})".format(type(flags).__name__)
            )
        self.flags = flags
        if self.__exception is not None:
>           raise self.__exception
E           test_utils.MockException: Should suppress

test_utils.py:93: MockException

During handling of the above exception, another exception occurred:

self = <paasio_test.PaasioTest testMethod=test_meteredsocket_context_manager_exception_suppress>

    def test_meteredsocket_context_manager_exception_suppress(self):
        exception = MockException("Should suppress")
        wrapped = MockSock(exception=exception)
        mock = NonCallableMagicMock(wraps=wrapped, autospec=True)
        mock.__exit__.side_effect = wrapped.__exit__
>       with MeteredSocket(mock) as socket:

paasio_test.py:49: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <paasio.MeteredSocket object at 0x7c062cf6e390>
exc_type = <class 'test_utils.MockException'>
exc_val = MockException('Should suppress')
exc_tb = <traceback object at 0x7c062c35f5c0>

    def __exit__(self, exc_type, exc_val, exc_tb):
>       self._socket.close()

paasio.py:89: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <NonCallableMagicMock id='136365953304016'>, name = 'close'

    def __getattr__(self, name):
        if name in {'_mock_methods', '_mock_unsafe'}:
            raise AttributeError(name)
        elif self._mock_methods is not None:
            if name not in self._mock_methods or name in _all_magics:
                raise AttributeError("Mock object has no attribute %r" % name)
        elif _is_magic(name):
            raise AttributeError(name)
        if not self._mock_unsafe and (not self._mock_methods or name not in self._mock_methods):
            if name.startswith(('assert', 'assret', 'asert', 'aseert', 'assrt')):
                raise AttributeError(
                    f"{name!r} is not a valid assertion. Use a spec "
                    f"for the mock if {name!r} is meant to be an attribute.")
    
        with NonCallableMock._lock:
            result = self._mock_children.get(name)
            if result is _deleted:
                raise AttributeError(name)
            elif result is None:
                wraps = None
                if self._mock_wraps is not None:
                    # XXXX should we get the attribute without triggering code
                    # execution?
>                   wraps = getattr(self._mock_wraps, name)
E                   AttributeError: 'MockSock' object has no attribute 'close'

/opt/miniconda3/lib/python3.11/unittest/mock.py:671: AttributeError
_________________ PaasioTest.test_meteredsocket_flags_support __________________

self = <paasio_test.PaasioTest testMethod=test_meteredsocket_flags_support>

    def test_meteredsocket_flags_support(self):
        mock = NonCallableMagicMock(wraps=MockSock(), autospec=True)
>       with MeteredSocket(mock) as socket:

paasio_test.py:159: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <paasio.MeteredSocket object at 0x7c062c110dd0>, exc_type = None
exc_val = None, exc_tb = None

    def __exit__(self, exc_type, exc_val, exc_tb):
>       self._socket.close()

paasio.py:89: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <NonCallableMagicMock id='136365950961552'>, name = 'close'

    def __getattr__(self, name):
        if name in {'_mock_methods', '_mock_unsafe'}:
            raise AttributeError(name)
        elif self._mock_methods is not None:
            if name not in self._mock_methods or name in _all_magics:
                raise AttributeError("Mock object has no attribute %r" % name)
        elif _is_magic(name):
            raise AttributeError(name)
        if not self._mock_unsafe and (not self._mock_methods or name not in self._mock_methods):
            if name.startswith(('assert', 'assret', 'asert', 'aseert', 'assrt')):
                raise AttributeError(
                    f"{name!r} is not a valid assertion. Use a spec "
                    f"for the mock if {name!r} is meant to be an attribute.")
    
        with NonCallableMock._lock:
            result = self._mock_children.get(name)
            if result is _deleted:
                raise AttributeError(name)
            elif result is None:
                wraps = None
                if self._mock_wraps is not None:
                    # XXXX should we get the attribute without triggering code
                    # execution?
>                   wraps = getattr(self._mock_wraps, name)
E                   AttributeError: 'MockSock' object has no attribute 'close'

/opt/miniconda3/lib/python3.11/unittest/mock.py:671: AttributeError
_________________ PaasioTest.test_meteredsocket_recv_multiple __________________

self = <paasio_test.PaasioTest testMethod=test_meteredsocket_recv_multiple>

    def test_meteredsocket_recv_multiple(self):
        wrapped = MockSock()
        mock = NonCallableMagicMock(wraps=wrapped, autospec=True)
        actual_recv = b""
>       with MeteredSocket(mock) as socket:

paasio_test.py:72: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <paasio.MeteredSocket object at 0x7c062c379d90>, exc_type = None
exc_val = None, exc_tb = None

    def __exit__(self, exc_type, exc_val, exc_tb):
>       self._socket.close()

paasio.py:89: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <NonCallableMagicMock id='136365953497552'>, name = 'close'

    def __getattr__(self, name):
        if name in {'_mock_methods', '_mock_unsafe'}:
            raise AttributeError(name)
        elif self._mock_methods is not None:
            if name not in self._mock_methods or name in _all_magics:
                raise AttributeError("Mock object has no attribute %r" % name)
        elif _is_magic(name):
            raise AttributeError(name)
        if not self._mock_unsafe and (not self._mock_methods or name not in self._mock_methods):
            if name.startswith(('assert', 'assret', 'asert', 'aseert', 'assrt')):
                raise AttributeError(
                    f"{name!r} is not a valid assertion. Use a spec "
                    f"for the mock if {name!r} is meant to be an attribute.")
    
        with NonCallableMock._lock:
            result = self._mock_children.get(name)
            if result is _deleted:
                raise AttributeError(name)
            elif result is None:
                wraps = None
                if self._mock_wraps is not None:
                    # XXXX should we get the attribute without triggering code
                    # execution?
>                   wraps = getattr(self._mock_wraps, name)
E                   AttributeError: 'MockSock' object has no attribute 'close'

/opt/miniconda3/lib/python3.11/unittest/mock.py:671: AttributeError
______________ PaasioTest.test_meteredsocket_recv_multiple_chunk _______________

self = <paasio_test.PaasioTest testMethod=test_meteredsocket_recv_multiple_chunk>

    def test_meteredsocket_recv_multiple_chunk(self):
        wrapped = MockSock(chunk=20)
        mock = NonCallableMagicMock(wraps=wrapped, autospec=True)
        actual_recv = b""
>       with MeteredSocket(mock) as socket:

paasio_test.py:84: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <paasio.MeteredSocket object at 0x7c062c2f04d0>, exc_type = None
exc_val = None, exc_tb = None

    def __exit__(self, exc_type, exc_val, exc_tb):
>       self._socket.close()

paasio.py:89: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <NonCallableMagicMock id='136365952932688'>, name = 'close'

    def __getattr__(self, name):
        if name in {'_mock_methods', '_mock_unsafe'}:
            raise AttributeError(name)
        elif self._mock_methods is not None:
            if name not in self._mock_methods or name in _all_magics:
                raise AttributeError("Mock object has no attribute %r" % name)
        elif _is_magic(name):
            raise AttributeError(name)
        if not self._mock_unsafe and (not self._mock_methods or name not in self._mock_methods):
            if name.startswith(('assert', 'assret', 'asert', 'aseert', 'assrt')):
                raise AttributeError(
                    f"{name!r} is not a valid assertion. Use a spec "
                    f"for the mock if {name!r} is meant to be an attribute.")
    
        with NonCallableMock._lock:
            result = self._mock_children.get(name)
            if result is _deleted:
                raise AttributeError(name)
            elif result is None:
                wraps = None
                if self._mock_wraps is not None:
                    # XXXX should we get the attribute without triggering code
                    # execution?
>                   wraps = getattr(self._mock_wraps, name)
E                   AttributeError: 'MockSock' object has no attribute 'close'

/opt/miniconda3/lib/python3.11/unittest/mock.py:671: AttributeError
___________________ PaasioTest.test_meteredsocket_recv_once ____________________

self = <paasio_test.PaasioTest testMethod=test_meteredsocket_recv_once>

    def test_meteredsocket_recv_once(self):
        mock = NonCallableMagicMock(wraps=MockSock(), autospec=True)
>       with MeteredSocket(mock) as socket:

paasio_test.py:61: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <paasio.MeteredSocket object at 0x7c062dcf4910>, exc_type = None
exc_val = None, exc_tb = None

    def __exit__(self, exc_type, exc_val, exc_tb):
>       self._socket.close()

paasio.py:89: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <NonCallableMagicMock id='136365953089168'>, name = 'close'

    def __getattr__(self, name):
        if name in {'_mock_methods', '_mock_unsafe'}:
            raise AttributeError(name)
        elif self._mock_methods is not None:
            if name not in self._mock_methods or name in _all_magics:
                raise AttributeError("Mock object has no attribute %r" % name)
        elif _is_magic(name):
            raise AttributeError(name)
        if not self._mock_unsafe and (not self._mock_methods or name not in self._mock_methods):
            if name.startswith(('assert', 'assret', 'asert', 'aseert', 'assrt')):
                raise AttributeError(
                    f"{name!r} is not a valid assertion. Use a spec "
                    f"for the mock if {name!r} is meant to be an attribute.")
    
        with NonCallableMock._lock:
            result = self._mock_children.get(name)
            if result is _deleted:
                raise AttributeError(name)
            elif result is None:
                wraps = None
                if self._mock_wraps is not None:
                    # XXXX should we get the attribute without triggering code
                    # execution?
>                   wraps = getattr(self._mock_wraps, name)
E                   AttributeError: 'MockSock' object has no attribute 'close'

/opt/miniconda3/lib/python3.11/unittest/mock.py:671: AttributeError
________________ PaasioTest.test_meteredsocket_recv_under_size _________________

self = <paasio_test.PaasioTest testMethod=test_meteredsocket_recv_under_size>

    def test_meteredsocket_recv_under_size(self):
        wrapped = MockSock(chunk=257)  # largish odd number
        mock = NonCallableMagicMock(wraps=wrapped, autospec=True)
>       with MeteredSocket(mock) as socket:

paasio_test.py:96: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <paasio.MeteredSocket object at 0x7c062c2c4990>, exc_type = None
exc_val = None, exc_tb = None

    def __exit__(self, exc_type, exc_val, exc_tb):
>       self._socket.close()

paasio.py:89: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <NonCallableMagicMock id='136365952756176'>, name = 'close'

    def __getattr__(self, name):
        if name in {'_mock_methods', '_mock_unsafe'}:
            raise AttributeError(name)
        elif self._mock_methods is not None:
            if name not in self._mock_methods or name in _all_magics:
                raise AttributeError("Mock object has no attribute %r" % name)
        elif _is_magic(name):
            raise AttributeError(name)
        if not self._mock_unsafe and (not self._mock_methods or name not in self._mock_methods):
            if name.startswith(('assert', 'assret', 'asert', 'aseert', 'assrt')):
                raise AttributeError(
                    f"{name!r} is not a valid assertion. Use a spec "
                    f"for the mock if {name!r} is meant to be an attribute.")
    
        with NonCallableMock._lock:
            result = self._mock_children.get(name)
            if result is _deleted:
                raise AttributeError(name)
            elif result is None:
                wraps = None
                if self._mock_wraps is not None:
                    # XXXX should we get the attribute without triggering code
                    # execution?
>                   wraps = getattr(self._mock_wraps, name)
E                   AttributeError: 'MockSock' object has no attribute 'close'

/opt/miniconda3/lib/python3.11/unittest/mock.py:671: AttributeError
_________________ PaasioTest.test_meteredsocket_send_multiple __________________

self = <paasio_test.PaasioTest testMethod=test_meteredsocket_send_multiple>

    def test_meteredsocket_send_multiple(self):
        wrapped = MockSock()
        mock = NonCallableMagicMock(wraps=wrapped, autospec=True)
        send_len = 0
        expected = b"Tomorrow's victory is today's practice."
>       with MeteredSocket(mock) as socket:

paasio_test.py:119: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <paasio.MeteredSocket object at 0x7c062c2f6e90>, exc_type = None
exc_val = None, exc_tb = None

    def __exit__(self, exc_type, exc_val, exc_tb):
>       self._socket.close()

paasio.py:89: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <NonCallableMagicMock id='136365952944144'>, name = 'close'

    def __getattr__(self, name):
        if name in {'_mock_methods', '_mock_unsafe'}:
            raise AttributeError(name)
        elif self._mock_methods is not None:
            if name not in self._mock_methods or name in _all_magics:
                raise AttributeError("Mock object has no attribute %r" % name)
        elif _is_magic(name):
            raise AttributeError(name)
        if not self._mock_unsafe and (not self._mock_methods or name not in self._mock_methods):
            if name.startswith(('assert', 'assret', 'asert', 'aseert', 'assrt')):
                raise AttributeError(
                    f"{name!r} is not a valid assertion. Use a spec "
                    f"for the mock if {name!r} is meant to be an attribute.")
    
        with NonCallableMock._lock:
            result = self._mock_children.get(name)
            if result is _deleted:
                raise AttributeError(name)
            elif result is None:
                wraps = None
                if self._mock_wraps is not None:
                    # XXXX should we get the attribute without triggering code
                    # execution?
>                   wraps = getattr(self._mock_wraps, name)
E                   AttributeError: 'MockSock' object has no attribute 'close'

/opt/miniconda3/lib/python3.11/unittest/mock.py:671: AttributeError
___________________ PaasioTest.test_meteredsocket_send_once ____________________

self = <paasio_test.PaasioTest testMethod=test_meteredsocket_send_once>

    def test_meteredsocket_send_once(self):
        wrapped = MockSock(chunk=257)  # largish odd number
        mock = NonCallableMagicMock(wraps=wrapped, autospec=True)
>       with MeteredSocket(mock) as socket:

paasio_test.py:106: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <paasio.MeteredSocket object at 0x7c062c2e8c50>, exc_type = None
exc_val = None, exc_tb = None

    def __exit__(self, exc_type, exc_val, exc_tb):
>       self._socket.close()

paasio.py:89: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <NonCallableMagicMock id='136365952900880'>, name = 'close'

    def __getattr__(self, name):
        if name in {'_mock_methods', '_mock_unsafe'}:
            raise AttributeError(name)
        elif self._mock_methods is not None:
            if name not in self._mock_methods or name in _all_magics:
                raise AttributeError("Mock object has no attribute %r" % name)
        elif _is_magic(name):
            raise AttributeError(name)
        if not self._mock_unsafe and (not self._mock_methods or name not in self._mock_methods):
            if name.startswith(('assert', 'assret', 'asert', 'aseert', 'assrt')):
                raise AttributeError(
                    f"{name!r} is not a valid assertion. Use a spec "
                    f"for the mock if {name!r} is meant to be an attribute.")
    
        with NonCallableMock._lock:
            result = self._mock_children.get(name)
            if result is _deleted:
                raise AttributeError(name)
            elif result is None:
                wraps = None
                if self._mock_wraps is not None:
                    # XXXX should we get the attribute without triggering code
                    # execution?
>                   wraps = getattr(self._mock_wraps, name)
E                   AttributeError: 'MockSock' object has no attribute 'close'

/opt/miniconda3/lib/python3.11/unittest/mock.py:671: AttributeError
________________ PaasioTest.test_meteredsocket_send_under_size _________________

self = <paasio_test.PaasioTest testMethod=test_meteredsocket_send_under_size>

    def test_meteredsocket_send_under_size(self):
        wrapped = MockSock(chunk=257)  # largish odd number
        mock = NonCallableMagicMock(wraps=wrapped, autospec=True)
>       with MeteredSocket(mock) as socket:

paasio_test.py:133: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <paasio.MeteredSocket object at 0x7c062c339690>, exc_type = None
exc_val = None, exc_tb = None

    def __exit__(self, exc_type, exc_val, exc_tb):
>       self._socket.close()

paasio.py:89: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <NonCallableMagicMock id='136365953220816'>, name = 'close'

    def __getattr__(self, name):
        if name in {'_mock_methods', '_mock_unsafe'}:
            raise AttributeError(name)
        elif self._mock_methods is not None:
            if name not in self._mock_methods or name in _all_magics:
                raise AttributeError("Mock object has no attribute %r" % name)
        elif _is_magic(name):
            raise AttributeError(name)
        if not self._mock_unsafe and (not self._mock_methods or name not in self._mock_methods):
            if name.startswith(('assert', 'assret', 'asert', 'aseert', 'assrt')):
                raise AttributeError(
                    f"{name!r} is not a valid assertion. Use a spec "
                    f"for the mock if {name!r} is meant to be an attribute.")
    
        with NonCallableMock._lock:
            result = self._mock_children.get(name)
            if result is _deleted:
                raise AttributeError(name)
            elif result is None:
                wraps = None
                if self._mock_wraps is not None:
                    # XXXX should we get the attribute without triggering code
                    # execution?
>                   wraps = getattr(self._mock_wraps, name)
E                   AttributeError: 'MockSock' object has no attribute 'close'

/opt/miniconda3/lib/python3.11/unittest/mock.py:671: AttributeError
________________ PaasioTest.test_meteredsocket_stats_read_only _________________

self = <paasio_test.PaasioTest testMethod=test_meteredsocket_stats_read_only>

    def test_meteredsocket_stats_read_only(self):
        mock = NonCallableMagicMock(wraps=MockSock(), autospec=True)
>       with MeteredSocket(mock) as socket:

paasio_test.py:187: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <paasio.MeteredSocket object at 0x7c062c2b7050>, exc_type = None
exc_val = None, exc_tb = None

    def __exit__(self, exc_type, exc_val, exc_tb):
>       self._socket.close()

paasio.py:89: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <NonCallableMagicMock id='136365952691408'>, name = 'close'

    def __getattr__(self, name):
        if name in {'_mock_methods', '_mock_unsafe'}:
            raise AttributeError(name)
        elif self._mock_methods is not None:
            if name not in self._mock_methods or name in _all_magics:
                raise AttributeError("Mock object has no attribute %r" % name)
        elif _is_magic(name):
            raise AttributeError(name)
        if not self._mock_unsafe and (not self._mock_methods or name not in self._mock_methods):
            if name.startswith(('assert', 'assret', 'asert', 'aseert', 'assrt')):
                raise AttributeError(
                    f"{name!r} is not a valid assertion. Use a spec "
                    f"for the mock if {name!r} is meant to be an attribute.")
    
        with NonCallableMock._lock:
            result = self._mock_children.get(name)
            if result is _deleted:
                raise AttributeError(name)
            elif result is None:
                wraps = None
                if self._mock_wraps is not None:
                    # XXXX should we get the attribute without triggering code
                    # execution?
>                   wraps = getattr(self._mock_wraps, name)
E                   AttributeError: 'MockSock' object has no attribute 'close'

/opt/miniconda3/lib/python3.11/unittest/mock.py:671: AttributeError
=========================== short test summary info ============================
FAILED paasio_test.py::PaasioTest::test_meteredfile_context_manager - ValueEr...
FAILED paasio_test.py::PaasioTest::test_meteredfile_context_manager_exception_raise
FAILED paasio_test.py::PaasioTest::test_meteredfile_context_manager_exception_suppress
FAILED paasio_test.py::PaasioTest::test_meteredfile_iteration - ValueError: I...
FAILED paasio_test.py::PaasioTest::test_meteredfile_read_multiple - ValueErro...
FAILED paasio_test.py::PaasioTest::test_meteredfile_read_multiple_chunk - Val...
FAILED paasio_test.py::PaasioTest::test_meteredfile_read_once - ValueError: I...
FAILED paasio_test.py::PaasioTest::test_meteredfile_read_under_size - ValueEr...
FAILED paasio_test.py::PaasioTest::test_meteredfile_stats_read_only - ValueEr...
FAILED paasio_test.py::PaasioTest::test_meteredfile_write_multiple - ValueErr...
FAILED paasio_test.py::PaasioTest::test_meteredfile_write_once - ValueError: ...
FAILED paasio_test.py::PaasioTest::test_meteredfile_write_under_size - ValueE...
FAILED paasio_test.py::PaasioTest::test_meteredsocket_bufsize_required - Attr...
FAILED paasio_test.py::PaasioTest::test_meteredsocket_context_manager - Attri...
FAILED paasio_test.py::PaasioTest::test_meteredsocket_context_manager_exception_raise
FAILED paasio_test.py::PaasioTest::test_meteredsocket_context_manager_exception_suppress
FAILED paasio_test.py::PaasioTest::test_meteredsocket_flags_support - Attribu...
FAILED paasio_test.py::PaasioTest::test_meteredsocket_recv_multiple - Attribu...
FAILED paasio_test.py::PaasioTest::test_meteredsocket_recv_multiple_chunk - A...
FAILED paasio_test.py::PaasioTest::test_meteredsocket_recv_once - AttributeEr...
FAILED paasio_test.py::PaasioTest::test_meteredsocket_recv_under_size - Attri...
FAILED paasio_test.py::PaasioTest::test_meteredsocket_send_multiple - Attribu...
FAILED paasio_test.py::PaasioTest::test_meteredsocket_send_once - AttributeEr...
FAILED paasio_test.py::PaasioTest::test_meteredsocket_send_under_size - Attri...
FAILED paasio_test.py::PaasioTest::test_meteredsocket_stats_read_only - Attri...
============================== 25 failed in 6.59s ==============================

2025-05-10 02:44:45,408 - ThreadPoolExecutor-0_17 - INFO - Attempting to stop container pb.eval.python__paasio.20250510_023940_090074...
2025-05-10 02:45:02,359 - ThreadPoolExecutor-0_17 - INFO - Attempting to remove container pb.eval.python__paasio.20250510_023940_090074...
2025-05-10 02:45:11,499 - ThreadPoolExecutor-0_17 - INFO - Container pb.eval.python__paasio.20250510_023940_090074 removed.
