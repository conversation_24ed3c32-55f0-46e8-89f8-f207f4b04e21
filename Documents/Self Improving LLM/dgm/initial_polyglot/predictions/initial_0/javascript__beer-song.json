{"instance_id": "javascript__beer-song", "model_name_or_path": "20250510_021328--claude-3-5-sonnet-20241022_0", "model_patch": "diff --git a/beer-song.js b/beer-song.js\nindex 8340bc2..8d0c1c5 100644\n--- a/beer-song.js\n+++ b/beer-song.js\n@@ -4,5 +4,38 @@\n //\n \n export const recite = (initialBottlesCount, takeDownCount) => {\n-  throw new Error('Remove this statement and implement this function');\n+  const verses = [];\n+\n+  const bottleLabel = (num) => {\n+    if (num === 0) return \"no more bottles\";\n+    if (num === 1) return \"1 bottle\";\n+    return `${num} bottles`;\n+  };\n+\n+  const capitalize = (str) => str.charAt(0).toUpperCase() + str.slice(1);\n+\n+  for (let i = initialBottlesCount; i > initialBottlesCount - takeDownCount; i--) {\n+    if (i > 0) {\n+      const current = `${bottleLabel(i)} of beer on the wall, ${bottleLabel(i)} of beer.`;\n+      let nextLabel = bottleLabel(i - 1);\n+      // Adjust wording for the action line\n+      let actionLine;\n+      if (i === 1) {\n+        actionLine = `Take it down and pass it around, ${nextLabel} of beer on the wall.`;\n+      } else {\n+        actionLine = `Take one down and pass it around, ${nextLabel} of beer on the wall.`;\n+      }\n+      verses.push(`${current}\\n${actionLine}`);\n+    } \n+  }\n+\n+  // After taking down the specified number of bottles, if we've reached 0 bottles, add the final verse\n+  if (initialBottlesCount - takeDownCount === 0) {\n+    verses.push(\n+      `No more bottles of beer on the wall, no more bottles of beer.\\n` +\n+      `Go to the store and buy some more, 99 bottles of beer on the wall.`\n+    );\n+  }\n+\n+  return verses.join(\"\\n\\n\");\n };\n", "proposed_model_patches": [], "eval_result": "unresolved", "success": true}