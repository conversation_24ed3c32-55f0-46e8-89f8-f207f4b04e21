["javascript__queen-attack", "rust__wordy", "python__dot-dsl", "java__satellite", "cpp__diamond", "rust__accumulate", "go__error-handling", "cpp__queen-attack", "rust__poker", "python__sgf-parsing", "rust__react", "java__ledger", "go__connect", "rust__macros", "javascript__triangle", "java__zipper", "java__bowling", "python__tree-building", "javascript__say", "java__wordy", "python__food-chain", "javascript__wordy", "python__poker", "javascript__grade-school", "cpp__gigasecond", "java__forth", "python__dominoes", "go__word-search", "javascript__simple-linked-list", "go__counter", "java__react", "javascript__ocr-numbers", "python__scale-generator", "java__go-counting", "rust__doubly-linked-list", "python__grade-school", "javascript__forth", "python__wordy", "java__mazy-mice", "cpp__bank-account", "python__zipper", "java__custom-set", "java__rest-api", "go__transpose", "rust__gigasecond", "rust__say", "go__food-chain", "rust__pig-latin", "go__markdown", "go__crypto-square"]