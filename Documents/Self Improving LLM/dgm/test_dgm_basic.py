#!/usr/bin/env python3
"""
Basic test for DGM with Ollama integration.
"""

import sys
import os
sys.path.append('.')

from ollama_config import OllamaConfig
from llm import create_client
from llm_withtools import chat_with_agent

def test_basic_dgm_functionality():
    """Test basic DGM functionality with Ollama."""
    print("Testing basic DGM functionality with Ollama...")
    
    # Test 1: Ollama connection
    print("\n1. Testing Ollama connection...")
    config = OllamaConfig()
    if not config.connect():
        print("❌ Failed to connect to Ollama")
        return False
    
    if not config.available_models:
        print("❌ No models available")
        return False
    
    model_name = config.available_models[0]['name']
    print(f"✅ Connected to Ollama, using model: {model_name}")
    
    # Test 2: LLM client creation
    print("\n2. Testing LLM client creation...")
    try:
        client, model = create_client(f"ollama/{model_name}")
        print(f"✅ Created client for model: {model}")
    except Exception as e:
        print(f"❌ Failed to create client: {e}")
        return False
    
    # Test 3: Basic chat functionality
    print("\n3. Testing basic chat functionality...")
    try:
        response = chat_with_agent(
            msg="Hello! Please respond with a simple greeting.",
            model=f"ollama/{model_name}",
            msg_history=[],
            logging=lambda x: None  # Silent logging
        )
        
        if response and len(response) > 0:
            print(f"✅ Chat working, got {len(response)} messages")
            print(f"   Last response preview: {response[-1]['content'][:100]}...")
        else:
            print("❌ Chat failed - no response")
            return False
            
    except Exception as e:
        print(f"❌ Chat failed with error: {e}")
        return False
    
    # Test 4: Import key DGM modules
    print("\n4. Testing DGM module imports...")
    try:
        from utils.common_utils import load_json_file
        from swe_bench.utils import setup_logger
        print("✅ DGM modules imported successfully")
    except Exception as e:
        print(f"❌ Failed to import DGM modules: {e}")
        return False
    
    # Test 5: Check SWE-bench data files
    print("\n5. Testing SWE-bench data files...")
    try:
        from utils.common_utils import load_json_file
        small_tasks = load_json_file("./swe_bench/subsets/small.json")
        print(f"✅ Loaded {len(small_tasks)} small SWE-bench tasks")
    except Exception as e:
        print(f"❌ Failed to load SWE-bench data: {e}")
        return False
    
    print("\n🎉 All basic tests passed! DGM should be ready to run.")
    return True

def main():
    print("=" * 60)
    print("Darwin Gödel Machine - Basic Functionality Test")
    print("=" * 60)
    
    success = test_basic_dgm_functionality()
    
    if success:
        print("\n✅ Basic functionality test PASSED")
        print("\nYou can now run DGM with:")
        print("python DGM_outer.py --model ollama/qwen3:30b-a3b --skip_model_selection --max_generation 5 --shallow_eval")
        return 0
    else:
        print("\n❌ Basic functionality test FAILED")
        print("Please check the errors above before running DGM.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
