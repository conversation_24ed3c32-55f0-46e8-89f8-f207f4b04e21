"""
Ollama-specific model handling for Darwin Gödel Machine.
"""
import json
import re
import ollama
import backoff
from typing import Dict, List, Optional, Any


class OllamaModelHandler:
    """Handler for Ollama model interactions."""
    
    def __init__(self, host: str = "http://localhost:11434"):
        """
        Initialize Ollama model handler.
        
        Args:
            host: Ollama server host URL
        """
        self.host = host
        self.client = ollama.Client(host=host)
    
    @backoff.on_exception(
        backoff.expo,
        (ConnectionError, TimeoutError, Exception),
        max_time=120,
        max_value=60,
    )
    def generate_response(
        self,
        model: str,
        messages: List[Dict],
        system_message: str = "",
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        stream: bool = False
    ) -> str:
        """
        Generate response from Ollama model.
        
        Args:
            model: Model name
            messages: List of message dictionaries
            system_message: System prompt
            temperature: Sampling temperature
            max_tokens: Maximum tokens to generate
            stream: Whether to stream response
            
        Returns:
            Generated response text
        """
        try:
            # Convert messages to Ollama format
            ollama_messages = self._convert_messages_to_ollama(messages, system_message)
            
            # Generate response
            response = self.client.chat(
                model=model,
                messages=ollama_messages,
                options={
                    'temperature': temperature,
                    'num_predict': max_tokens or -1,
                },
                stream=stream
            )
            
            if stream:
                # Handle streaming response
                full_response = ""
                for chunk in response:
                    if 'message' in chunk and 'content' in chunk['message']:
                        full_response += chunk['message']['content']
                return full_response
            else:
                # Handle non-streaming response
                return response['message']['content']
                
        except Exception as e:
            raise Exception(f"Error generating response from Ollama: {e}")
    
    def _convert_messages_to_ollama(self, messages: List[Dict], system_message: str = "") -> List[Dict]:
        """
        Convert message format to Ollama-compatible format.
        
        Args:
            messages: List of message dictionaries
            system_message: System prompt to prepend
            
        Returns:
            List of Ollama-compatible message dictionaries
        """
        ollama_messages = []
        
        # Add system message if provided
        if system_message:
            ollama_messages.append({
                "role": "system",
                "content": system_message
            })
        
        for msg in messages:
            if isinstance(msg, dict):
                role = msg.get('role', 'user')
                content = msg.get('content', '')
                
                # Handle different content formats
                if isinstance(content, list):
                    # Extract text from content blocks
                    text_content = ""
                    for block in content:
                        if isinstance(block, dict):
                            if block.get('type') == 'text':
                                text_content += block.get('text', '')
                            elif block.get('type') == 'input_text':
                                text_content += block.get('text', '')
                    content = text_content
                
                ollama_messages.append({
                    "role": role,
                    "content": str(content)
                })
        
        return ollama_messages
    
    def check_model_availability(self, model_name: str) -> bool:
        """
        Check if a model is available in Ollama.
        
        Args:
            model_name: Name of the model to check
            
        Returns:
            bool: True if model is available, False otherwise
        """
        try:
            models = self.client.list()
            available_models = [m['name'] for m in models.get('models', [])]
            return model_name in available_models
        except Exception:
            return False
    
    def pull_model(self, model_name: str) -> bool:
        """
        Pull a model from Ollama registry.
        
        Args:
            model_name: Name of the model to pull
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            print(f"Pulling model {model_name}...")
            self.client.pull(model_name)
            print(f"Successfully pulled {model_name}")
            return True
        except Exception as e:
            print(f"Error pulling model {model_name}: {e}")
            return False
    
    def simulate_tool_calling(self, response: str, available_tools: List[Dict]) -> Optional[Dict]:
        """
        Simulate tool calling for models that don't support native tool calling.
        
        Args:
            response: Model response text
            available_tools: List of available tool definitions
            
        Returns:
            Tool call information if found, None otherwise
        """
        # Look for tool use patterns in the response
        tool_patterns = [
            r'<tool_use>\s*({.*?})\s*</tool_use>',
            r'```json\s*({.*?})\s*```',
            r'Tool:\s*(\w+)\s*Input:\s*({.*?})',
        ]
        
        for pattern in tool_patterns:
            matches = re.findall(pattern, response, re.DOTALL | re.IGNORECASE)
            for match in matches:
                try:
                    if isinstance(match, tuple):
                        # Handle tuple matches (e.g., from Tool: name Input: {...} pattern)
                        if len(match) == 2:
                            tool_name, tool_input_str = match
                            tool_input = json.loads(tool_input_str)
                            return {
                                'tool_name': tool_name,
                                'tool_input': tool_input
                            }
                    else:
                        # Handle single match (JSON object)
                        tool_call = json.loads(match)
                        if 'tool_name' in tool_call and 'tool_input' in tool_call:
                            return tool_call
                except json.JSONDecodeError:
                    continue
        
        return None
    
    def format_tool_prompt(self, available_tools: List[Dict]) -> str:
        """
        Format tool information for inclusion in prompts.
        
        Args:
            available_tools: List of available tool definitions
            
        Returns:
            Formatted tool prompt string
        """
        if not available_tools:
            return ""
        
        tool_descriptions = []
        for tool in available_tools:
            info = tool.get('info', {})
            name = info.get('name', 'unknown')
            description = info.get('description', 'No description')
            
            # Format input schema
            input_schema = info.get('input_schema', {})
            properties = input_schema.get('properties', {})
            required = input_schema.get('required', [])
            
            param_desc = []
            for param, details in properties.items():
                param_type = details.get('type', 'string')
                param_desc_text = details.get('description', '')
                required_marker = " (required)" if param in required else ""
                param_desc.append(f"  - {param} ({param_type}){required_marker}: {param_desc_text}")
            
            tool_desc = f"**{name}**: {description}"
            if param_desc:
                tool_desc += f"\n  Parameters:\n" + "\n".join(param_desc)
            
            tool_descriptions.append(tool_desc)
        
        return f"""
Available Tools:
{chr(10).join(tool_descriptions)}

To use a tool, format your response like this:
<tool_use>
{{"tool_name": "tool_name", "tool_input": {{"param1": "value1", "param2": "value2"}}}}
</tool_use>
"""


def create_ollama_client(model: str, host: str = "http://localhost:11434"):
    """
    Create an Ollama client for the specified model.
    
    Args:
        model: Model name
        host: Ollama server host URL
        
    Returns:
        Tuple of (OllamaModelHandler, model_name)
    """
    handler = OllamaModelHandler(host)
    
    # Verify model is available
    if not handler.check_model_availability(model):
        print(f"Model {model} not found. Available models:")
        try:
            models = handler.client.list()
            for m in models.get('models', []):
                print(f"  - {m['name']}")
        except Exception as e:
            print(f"Error listing models: {e}")
        raise ValueError(f"Model {model} not available in Ollama")
    
    return handler, model


# Recommended Ollama models for coding tasks
RECOMMENDED_CODING_MODELS = [
    "qwen2.5-coder:32b",
    "qwen2.5-coder:14b", 
    "qwen2.5-coder:7b",
    "deepseek-coder:33b",
    "deepseek-coder:6.7b",
    "codellama:34b",
    "codellama:13b",
    "codellama:7b",
    "llama3.1:70b",
    "llama3.1:8b",
    "mistral:7b",
    "qwen2.5:32b",
    "qwen2.5:14b",
    "qwen2.5:7b",
]


def suggest_models() -> None:
    """Print suggested models for coding tasks."""
    print("Recommended models for coding tasks:")
    print("=" * 50)
    for model in RECOMMENDED_CODING_MODELS:
        print(f"  - {model}")
    print("\nTo pull a model: ollama pull <model_name>")
    print("Example: ollama pull qwen2.5-coder:7b")
