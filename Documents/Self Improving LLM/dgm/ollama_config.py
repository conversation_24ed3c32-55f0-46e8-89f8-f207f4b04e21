"""
Ollama configuration and model selection interface for Darwin Gödel Machine.
"""
import json
import os
import ollama
from typing import List, Dict, Optional


class OllamaConfig:
    """Configuration manager for Ollama integration."""
    
    def __init__(self, host: str = "http://localhost:11434"):
        """
        Initialize Ollama configuration.
        
        Args:
            host: Ollama server host URL
        """
        self.host = host
        self.client = None
        self.available_models = []
        self.selected_model = None
        
    def connect(self) -> bool:
        """
        Connect to Ollama server and verify connection.
        
        Returns:
            bool: True if connection successful, False otherwise
        """
        try:
            self.client = ollama.Client(host=self.host)
            # Test connection by listing models
            self.available_models = self.list_models()
            return True
        except Exception as e:
            print(f"Failed to connect to Ollama at {self.host}: {e}")
            return False
    
    def list_models(self) -> List[Dict]:
        """
        List all available models in Ollama.
        
        Returns:
            List of model dictionaries with name, size, and other metadata
        """
        if not self.client:
            return []
        
        try:
            response = self.client.list()
            models = []
            for model in response.get('models', []):
                # Handle both old and new API response formats
                model_name = model.get('name') or model.get('model', '')
                models.append({
                    'name': model_name,
                    'size': model.get('size', 0),
                    'modified_at': model.get('modified_at', ''),
                    'digest': model.get('digest', ''),
                    'details': model.get('details', {})
                })
            return models
        except Exception as e:
            print(f"Error listing models: {e}")
            return []
    
    def display_models(self) -> None:
        """Display available models in a user-friendly format."""
        if not self.available_models:
            print("No models available. Please pull some models first using 'ollama pull <model_name>'")
            return
        
        print("\nAvailable Ollama Models:")
        print("-" * 60)
        for i, model in enumerate(self.available_models, 1):
            size_gb = model['size'] / (1024**3) if model['size'] > 0 else 0
            print(f"{i:2d}. {model['name']:<30} ({size_gb:.1f} GB)")
        print("-" * 60)
    
    def select_model_interactive(self) -> Optional[str]:
        """
        Interactive model selection interface.
        
        Returns:
            Selected model name or None if cancelled
        """
        if not self.available_models:
            print("No models available!")
            return None
        
        self.display_models()
        
        while True:
            try:
                choice = input(f"\nSelect a model (1-{len(self.available_models)}) or 'q' to quit: ").strip()
                
                if choice.lower() == 'q':
                    return None
                
                model_index = int(choice) - 1
                if 0 <= model_index < len(self.available_models):
                    selected_model = self.available_models[model_index]['name']
                    self.selected_model = selected_model
                    print(f"Selected model: {selected_model}")
                    return selected_model
                else:
                    print(f"Please enter a number between 1 and {len(self.available_models)}")
                    
            except ValueError:
                print("Please enter a valid number or 'q' to quit")
            except KeyboardInterrupt:
                print("\nSelection cancelled.")
                return None
    
    def get_model_info(self, model_name: str) -> Optional[Dict]:
        """
        Get detailed information about a specific model.
        
        Args:
            model_name: Name of the model
            
        Returns:
            Model information dictionary or None if not found
        """
        for model in self.available_models:
            if model['name'] == model_name:
                return model
        return None
    
    def save_config(self, config_path: str = "ollama_config.json") -> None:
        """
        Save current configuration to file.
        
        Args:
            config_path: Path to save configuration file
        """
        config = {
            'host': self.host,
            'selected_model': self.selected_model,
            'available_models': self.available_models
        }
        
        try:
            # Convert datetime objects to strings for JSON serialization
            config_serializable = {}
            for key, value in config.items():
                if key == 'available_models':
                    # Handle datetime in model metadata
                    models_serializable = []
                    for model in value:
                        model_copy = model.copy()
                        if 'modified_at' in model_copy and hasattr(model_copy['modified_at'], 'isoformat'):
                            model_copy['modified_at'] = model_copy['modified_at'].isoformat()
                        models_serializable.append(model_copy)
                    config_serializable[key] = models_serializable
                else:
                    config_serializable[key] = value

            with open(config_path, 'w') as f:
                json.dump(config_serializable, f, indent=2)
            print(f"Configuration saved to {config_path}")
        except Exception as e:
            print(f"Error saving configuration: {e}")
    
    def load_config(self, config_path: str = "ollama_config.json") -> bool:
        """
        Load configuration from file.
        
        Args:
            config_path: Path to configuration file
            
        Returns:
            bool: True if loaded successfully, False otherwise
        """
        try:
            if os.path.exists(config_path):
                with open(config_path, 'r') as f:
                    config = json.load(f)
                
                self.host = config.get('host', self.host)
                self.selected_model = config.get('selected_model')
                self.available_models = config.get('available_models', [])
                
                print(f"Configuration loaded from {config_path}")
                return True
        except Exception as e:
            print(f"Error loading configuration: {e}")
        
        return False


def setup_ollama_interactive() -> Optional[OllamaConfig]:
    """
    Interactive setup for Ollama configuration.
    
    Returns:
        Configured OllamaConfig instance or None if setup failed
    """
    print("=== Darwin Gödel Machine - Ollama Setup ===")
    
    # Try to load existing configuration
    config = OllamaConfig()
    if config.load_config():
        use_existing = input("Use existing configuration? (y/n): ").strip().lower()
        if use_existing == 'y' and config.selected_model:
            # Verify connection still works
            if config.connect():
                print(f"Using existing model: {config.selected_model}")
                return config
    
    # Get Ollama host
    host = input("Enter Ollama host URL (default: http://localhost:11434): ").strip()
    if not host:
        host = "http://localhost:11434"
    
    config = OllamaConfig(host)
    
    # Connect and list models
    print(f"Connecting to Ollama at {host}...")
    if not config.connect():
        print("Failed to connect to Ollama. Please ensure Ollama is running and accessible.")
        return None
    
    # Select model
    selected_model = config.select_model_interactive()
    if not selected_model:
        print("No model selected. Exiting setup.")
        return None
    
    # Save configuration
    config.save_config()
    
    return config


if __name__ == "__main__":
    # Test the configuration setup
    config = setup_ollama_interactive()
    if config:
        print(f"Setup complete! Selected model: {config.selected_model}")
    else:
        print("Setup failed or cancelled.")
